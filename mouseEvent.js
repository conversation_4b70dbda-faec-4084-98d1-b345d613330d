
window.tuneTransformAttr = function(innerDia, dx, dy) {

    var pelId = innerDia.diaId;
    // console.log("pelId: " + pelId);

    var gElement = $('#' + pelId);

    var transformAttr = gElement.attr('transform');
    // console.log("transformAttr:" + transformAttr);

    var tHeadIndex = transformAttr.indexOf('translate(');

    // console.log("tHeadIndex:" + tHeadIndex);
    var tHead = transformAttr.substr(0, (tHeadIndex + 10));
    // console.log("tHead:" + tHead);
    // console.log("transformAttr.length():" + transformAttr.length);
    var remain = transformAttr.substr((tHeadIndex + 10), transformAttr.length);
    // console.log("remain:" + remain);

    var tEndIndex = remain.indexOf(')');
    var xy = remain.substr(0, tEndIndex);
    // console.log("xy:" + xy);
    var tEnd = remain.substr(tEndIndex, remain.length);
    // console.log("tEnd:" + tEnd);


    var startX = innerDia.startX;
    var startY = innerDia.startY;
    // console.log("startX:" + startX + " || startY:" + startY);

    var x_new = parseFloat(startX) + parseFloat(dx);
    var y_new = parseFloat(startY) + parseFloat(dy);
    // console.log("x_new:" + x_new + " || y_new:" + y_new);
    var newTransform = tHead + x_new + " " + y_new + tEnd;
    // console.log("newTransform:" + newTransform);
    return newTransform;
}

window.mouseOpEvent = function (){

    // 先清除，否则点击事件都会重复两次
    mouseEventOff();
    // 取消事件监听器
    window.removeEventListener("keydown", keyDown, false);

    $("#drawPdSvg").bind("mousedown", function(event){

        event.preventDefault();
        event.stopPropagation();

        var targetElement = event.target;
        // console.log(targetElement);

        var tarName = targetElement.tagName;
        // console.log(tarName);

        var targetElementSVG = SVG.adopt(targetElement);
        var texttype = targetElementSVG.attr('texttype');
        var tarId = targetElementSVG.attr('id');
        var tarClass = targetElementSVG.attr('class');

        var diaHtml = document.getElementById(tarId);

        // console.log(targetElementSVG);
        // console.log(tarClass);

        // 大馈线基础信息部分
        if(tarClass == "basicInfo"){
            // 添加空白右键菜单来实现屏蔽右键事件
            $('.basicInfo').contextMenu('rmBlack', {	//空白右键
                menuStyle: {
                    width: 120,
                    left: 1240,
                    top: 465,
                },
                itemStyle: {
                    lineHeight: '20px',
                    padding: '1px 5px',
                    marginLeft: 14,
                    marginTop: 4,
                    marginBottom: 4,
                    width: 60
                },
                itemHoverStyle: {
                    backgroundColor: '#333',
                    color: '#eee',
                    cursor: 'pointer',
                },
                bindings: {

                }
            });
            return;
        }

        // 先恢复样式
        restoreDiaStyle(selectedDiaMap);
        // 然后清空selectedDiaMap
        selectedDiaMap = new Object();

        var tarPsrId_mapLocation = targetElementSVG.attr('psrId');
        if(tarPsrId_mapLocation != null && tarPsrId_mapLocation != "null" && tarPsrId_mapLocation != "" && typeof(tarPsrId_mapLocation) != "undefined"){
            gotoMap(tarPsrId_mapLocation);
        }

        if (event.button == 2) {    //右键事件
            if(changeModel){    // 可编辑状态
                if(tarName.toLowerCase() == "svg" || (tarName.toLowerCase() == "rect" && tarClass == "gridRect")) {
                    //鼠标点击处为空白
                    console.log("点击空白svg");
                    // TODO 屏蔽默认右键事件，未实现
                    // event.preventDefault(); // 确保这里也阻止默认行为
                    // event.stopPropagation(); // 并且停止事件冒泡

                    // 添加空白右键菜单来实现屏蔽右键事件
                    $('.gridRect').contextMenu('rmBlack', {	//空白右键
                        menuStyle: {
                            width: 120,
                            left: 1240,
                            top: 465,
                        },
                        itemStyle: {
                            lineHeight: '20px',
                            padding: '1px 5px',
                            marginLeft: 14,
                            marginTop: 4,
                            marginBottom: 4,
                            width: 60
                        },
                        itemHoverStyle: {
                            backgroundColor: '#333',
                            color: '#eee',
                            cursor: 'pointer',
                        },
                        bindings: {

                        }
                    });

                    return;
                }

                if(tarName.toLowerCase() == "text" || tarName.toLowerCase() == "tspan"){
                    // 标签
                    var tarId = "";
                    var tClass = "";
                    if(tarName.toLowerCase() == "tspan"){
                        var tParentNode = targetElement.parentNode;
                        var targetElementSVG_parant = SVG.adopt(tParentNode);
                        tarId = targetElementSVG_parant.attr('id');
                        tClass = targetElementSVG_parant.attr('class');
                    } else {
                        tarId = targetElementSVG.attr('id');
                        tClass = targetElementSVG.attr('class');
                    }
                    console.log(tClass);

                    $('.diaLabel, .meas_inline, .label_zf').contextMenu('rmDiaLabel', {	//图元标签右键
                        menuStyle: {
                            width: 140,
                            left: 1240,
                            top: 465,
                        },
                        itemStyle: {
                            lineHeight: '20px',
                            padding: '1px 5px',
                            marginLeft: 14,
                            marginTop: 4,
                            marginBottom: 4,
                            width: 100
                        },
                        itemHoverStyle: {
                            backgroundColor: '#333',
                            color: '#eee',
                            cursor: 'pointer',
                        },
                        bindings: {
                            'rmReBackDiaLabel': function(t) {
                                console.log("rmReBackDiaLabel");
                                rmReBackDiaLabel(tarId, tClass);
                            }
                        }
                    });
                }
                else {
                    // console.log("tarId:" + tarId);
                    var tarDHtml = document.getElementById(tarId);
                    // console.log(tarDHtml);

                    var diaGeoPsrId = tarDHtml.getAttribute("geoPsrId");
                    var rotate = tarDHtml.getAttribute("rotate");
                    var transform = tarDHtml.getAttribute("transform");

                    var psrId = tarDHtml.getAttribute("psrId");
                    var psrName = tarDHtml.getAttribute("psrName");

                    rotate = dealRotate(rotate);

                    var coordinates = tarDHtml.getAttribute("coordinates");



                    if(zoomDrag == true && tarId == 'zoomRect') {
                        // console.log("拉框内点击右键");

                        $('.zoomRect').contextMenu('rmZoomRect', {	//拉框范围右键
                            menuStyle: {
                                width: 120,
                                left: 1240,
                                top: 465,
                            },
                            itemStyle: {
                                lineHeight: '20px',
                                padding: '1px 5px',
                                marginLeft: 14,
                                marginTop: 4,
                                marginBottom: 4,
                                width: 60
                            },
                            itemHoverStyle: {
                                backgroundColor: '#333',
                                color: '#eee',
                                cursor: 'pointer',
                            },
                            bindings: {
                                'rmZoomRectMerge': function(t) {
                                    // 合并
                                    console.log("rmZoomRectMerge");
                                    // rightMenuRotate(rotate, tarDHtml, transform, tarId, coordinates, diaGeoPsrId);
                                    dealZoomRectMerge();
                                },
                                'rmDistribution_H': function(t) {
                                    // console.log("rmDistribution_H");
                                    rmDistribution("H");
                                },
                                'rmDistribution_V': function(t) {
                                    // console.log("rmDistribution_V");
                                    rmDistribution("V");
                                }
                            }
                        });

                    }
                    else {
                        $('.kv10_handSwitch0305').contextMenu('rm0305', {	//右键手车开关0305
                            menuStyle: {
                                width: 120,
                                left: 1240,
                                top: 465,
                            },
                            itemStyle: {
                                lineHeight: '20px',
                                padding: '1px 5px',
                                marginLeft: 14,
                                marginTop: 4,
                                marginBottom: 4,
                                width: 60
                            },
                            itemHoverStyle: {
                                backgroundColor: '#333',
                                color: '#eee',
                                cursor: 'pointer',
                            },
                            bindings: {
                                'rm0305Rotate': function(t) {
                                    console.log("rm0305Rotate");
                                    rightMenuRotate(rotate, tarDHtml, transform, tarId, coordinates, diaGeoPsrId);
                                },
                                'rm0305Detail': function(t) {
                                    console.log("rm0305Detail");
                                    detailInfo(tarId);
                                }
                            }
                        });

                        $('.kv10_zsBreaker0111, .disconnector, .dropOutFuse, .zsDisc, .cableTerminal, .outLinePoint, .fuseDisc_1, .trans0303, .disc_hand_0306, .discHand_fuse_0306, .transSingle0313, .trans_3_2_0313, .trans_vol_4_0314, .arresterGround').contextMenu('rm0111', {	//右键柱上断路器0111
                            menuStyle: {
                                width: 120,
                                left: 1240,
                                top: 465,
                            },
                            itemStyle: {
                                lineHeight: '20px',
                                padding: '1px 5px',
                                marginLeft: 14,
                                marginTop: 4,
                                marginBottom: 4,
                                width: 60
                            },
                            itemHoverStyle: {
                                backgroundColor: '#333',
                                color: '#eee',
                                cursor: 'pointer',
                            },
                            bindings: {
                                'rm0111Rotate': function(t) {
                                    console.log("rm0111Rotate");
                                    rightMenuRotate(rotate, tarDHtml, transform, tarId, coordinates, diaGeoPsrId);
                                }
                                ,'rm0111Detail': function(t) {
                                    console.log("查看详情");
                                    detailInfo(tarId);
                                }
                            }
                        });

                        $('.kv10_transformer2td').contextMenu('rm0302', {	//10kV-380V双绕组变压器
                            menuStyle: {
                                width: 140,
                                left: 1240,
                                top: 465,
                            },
                            itemStyle: {
                                lineHeight: '20px',
                                padding: '1px 5px',
                                marginLeft: 14,
                                marginTop: 4,
                                marginBottom: 4,
                                width: 100
                            },
                            itemHoverStyle: {
                                backgroundColor: '#333',
                                color: '#eee',
                                cursor: 'pointer',
                            },
                            bindings: {
                                'rm0302Rotate': function(t) {
                                    console.log("rm0302Rotate");
                                    rightMenuRotate(rotate, tarDHtml, transform, tarId, coordinates, diaGeoPsrId);
                                },
                                'rm0302IntoUser': function(t) {
                                    console.log("rm0302IntoUser");
                                    alert("liyue1")
                                     getZoneInfoByTransPsrId(psrId);
                                },
                                'rm0302MeasChart': function(t) {
                                    console.log("rm0302MeasChart");
                                    console.log("查看配变" + psrId + "量测曲线");
                                    // getTransMeasCharts_yzt(psrId, psrName);

                                    getTransMeasCharts(psrId, psrName);
                                }
                                ,'rm0302Fac2Path': function(t) {
                                    console.log("查看变电站到配变的路径");
                                    fac2Path(tarId);
                                }
                                ,'rm0302Detail': function(t) {
                                    console.log("查看详情");
                                    detailInfo(tarId);
                                }
                            }
                        });

                        $('.kv10_transformer0110').contextMenu('rm0110', {	//柱上用户变压器
                            menuStyle: {
                                width: 140,
                                left: 1240,
                                top: 465,
                            },
                            itemStyle: {
                                lineHeight: '20px',
                                padding: '1px 5px',
                                marginLeft: 14,
                                marginTop: 4,
                                marginBottom: 4,
                                width: 100
                            },
                            itemHoverStyle: {
                                backgroundColor: '#333',
                                color: '#eee',
                                cursor: 'pointer',
                            },
                            bindings: {
                                'rm0110Rotate': function(t) {
                                    console.log("rm0110Rotate");
                                    rightMenuRotate(rotate, tarDHtml, transform, tarId, coordinates, diaGeoPsrId);
                                },
                                'rm0110IntoUser': function(t) {
                                    console.log("rm0110IntoUser");
                                    getZoneInfoByTransPsrId(psrId);
                                },
                                'rm0110IntoUser2': function(t) {
                                    console.log("rm0110IntoUser2");
                                    //    alert("liyue2=" +psrId);
                                    //  getZoneInfoByTransPsrId(psrId);
                                    // 请确保你已经通过 layui.use(['layer', 'form', 'dropdown']) 加载了所有需要的模块
                                    layer.open({
                                        type: 2,
                                        title: '二叉图',
                                        area: ['90%', '90%'],
                                        shade: 0,
                                        maxmin: true,
                                        resize: true,
                                        content: ['binary_tree.html?psrId=' + psrId],
                                        end: function () {}
                                    });
                                },
                                'rm0110MeasChart': function(t) {
                                    console.log("rm0110MeasChart");
                                    console.log("查看配变" + psrId + "量测曲线");
                                    // getTransMeasCharts_yzt(psrId, psrName);

                                    getTransMeasCharts(psrId, psrName);
                                }
                                ,'rm0110Fac2Path': function(t) {
                                    console.log("查看变电站到配变的路径");
                                    fac2Path(tarId);
                                }
                                ,'rm0110Detail': function(t) {
                                    console.log("查看详情");
                                    detailInfo(tarId);
                                }
                            }
                        });



                        $('.virtuallyLine').contextMenu('rmVirtuallyLine', {	//线路添加右键
                            menuStyle: {
                                width: 140,
                                left: 1240,
                                top: 465,
                            },
                            itemStyle: {
                                lineHeight: '20px',
                                padding: '1px 5px',
                                marginLeft: 14,
                                marginTop: 4,
                                marginBottom: 4,
                                width: 100
                            },
                            itemHoverStyle: {
                                backgroundColor: '#333',
                                color: '#eee',
                                cursor: 'pointer',
                            },
                            bindings: {
                                'rmAddPoint': function(t) {
                                    console.log("rmAddPoint");
                                    var pointX = event.clientX;
                                    var pointY = event.clientY;
                                    addPoint(tarId, pointX, pointY);
                                }
                                ,'rmLineDetail': function(t) {
                                    console.log("rmLineDetail");
                                    lineDetailInfo(tarId);
                                }
                            }
                        });

                        $('.virtualPoint').contextMenu('rmVLPoint', {	//线路拐点右键
                            menuStyle: {
                                width: 140,
                                left: 1240,
                                top: 465,
                            },
                            itemStyle: {
                                lineHeight: '20px',
                                padding: '1px 5px',
                                marginLeft: 14,
                                marginTop: 4,
                                marginBottom: 4,
                                width: 100
                            },
                            itemHoverStyle: {
                                backgroundColor: '#333',
                                color: '#eee',
                                cursor: 'pointer',
                            },
                            bindings: {
                                'rmRemoveVirtualNode': function(t) {
                                    console.log("rmRemoveVirtualNode");
                                    removeVirtualNode(tarId);
                                }
                            }
                        });
                    }
                }

            }
        }
        else {
            //左击
            // console.log("mousedown-zoomDrag:" + zoomDrag + " || tarId:" + tarId);
            if(zoomDrag == true && tarId == 'zoomRect') {
                if(changeModel) {   // 可编辑状态
                    //如果当前处于拉框模式，并且点击处为拉框范围内
                    // console.log("zoomMove ------------");
                    mouseDownType = "zoomMove";
                    startPoint = {x: event.clientX, y: event.clientY};

                    cobj=SVG(targetElement);
                    fx=cobj.cx();
                    fy=cobj.cy();
                    deltax = event.clientX;
                    deltay = event.clientY;
                }
            }
            else {	//非拉框模式（包括拉框后，鼠标在框外点击）
                if(zoomDrag == true){
                    //TODO 做一些处理
                    if(changeModel) {   // 可编辑状态
                        rectSelG = [];	//存储拉框选中的普通图元
                        rectSelG_Line = [];	//存储拉框选中的线路（母线）图元，后续考虑开闭所的框体也用此变量
                        rectSelG_VirtualPoint = [];	//存储拉框选中的拐点
                        rectSelG_busPoint = [];	//存储拉框选中的母线连接点

                        mouseDownType = "";
                        zoomDrag = false;

                        // 点击不在拉框范围内，则先让拉框框体消失
                        selectionBox.attr({ x: event.clientX, y: event.clientY, width: 0, height: 0 });
                    }
                }
                if(tarName.toLowerCase() == "svg" || (tarName.toLowerCase() == "rect" && tarClass == "gridRect")) {
                    if(changeModel) {   // 可编辑状态
                        //鼠标点击处为空白
                        // console.log("点击空白svg");
                        mouseDownType = "black";

                        var mouseX_now, mouseY_now;

                        mouseX_now = event.clientX;
                        mouseY_now = event.clientY;

                        zoomStartPoint = {x: mouseX_now, y: mouseY_now};
                        zoomEndPoint = {x: mouseX_now, y: mouseY_now};

                        startPoint = {x: event.clientX, y: event.clientY};

                        selectionBox.attr({ x: mouseX_now, y: mouseY_now, width: 0, height: 0 });

                        //获取拉框矩形svg坐标与document坐标的区别。
                        var zoomRectDoc = document.getElementById('zoomRect');
                        var zoomRectDoc_rect = zoomRectDoc.getBoundingClientRect();	//文档中的位置

                        var selectionBoxX_svg = selectionBox.attr('x');
                        var selectionBoxY_svg = selectionBox.attr('y');

                        var selectionBoxX_doc = zoomRectDoc_rect.left;
                        var selectionBoxY_doc = zoomRectDoc_rect.top;

                        zoomDisX = (selectionBoxX_doc - selectionBoxX_svg);
                        zoomDisY = (selectionBoxY_doc - selectionBoxY_svg);
                    }
                }
                else if(tarName.toLowerCase() == "line") {
                    var psrType = targetElementSVG.attr("psrType");
                    if(psrType == "0311"){
                        // 改变点击的图元的样式
                        changeDiaStyle(tarId, busLine_sel_sty);
                        selectedDiaMap[tarId] = selDia(tarId, busLine_sty);
                    }
                    var lineType = targetElementSVG.attr("lineType");
                    if(lineType == "virtuallyLine"){
                        var oldStyle = targetElementSVG.attr("style");
                        // console.log("oldStyle: " + oldStyle);
                        if(oldStyle.includes("dasharray")){
                            // console.log("是虚线");
                            changeDiaStyle(tarId, virtuallyLine_dashed_sel_sty);
                            selectedDiaMap[tarId] = selDia(tarId, virtuallyLine_dashed_sty);
                        } else {
                            changeDiaStyle(tarId, virtuallyLine_sel_sty);
                            selectedDiaMap[tarId] = selDia(tarId, virtuallyLine_sty);
                        }

                        // changeDiaStyle(tarId, virtuallyLine_sel_sty);
                        // selectedDiaMap[tarId] = selDia(tarId, virtuallyLine_sty);
                    }

                    if(changeModel) {
                        // 可编辑状态
                        // console.log("line线路类型");
                        // var tarId = targetElementSVG.attr('id');
                        var psrId = targetElementSVG.attr('psrId');
                        var gisOId = targetElementSVG.attr('gisOId');
                        var geoPsrId = targetElementSVG.attr('geoPsrId');
                        var connection = targetElementSVG.attr('connection');
                        // console.log("tarId: " + tarId + " || psrId：" + psrId + " || geoPsrId:" + geoPsrId + " || connection:" + connection);
                        // console.log(typeof connection);
                        if(typeof connection === 'number'){
                            connection = connection + "";
                        }

                        if(geoPsrId == null || typeof geoPsrId == "undefined"){
                            return;
                        }

                        var tarDHtml = document.getElementById(tarId);
                        var diaGeoPsrId = tarDHtml.getAttribute("geoPsrId");
                        var psrTypeName = tarDHtml.getAttribute("psrTypeName");
                        /*if(psrTypeName == "导线段"){
                        }*/
                        mouseDownType = psrTypeName;

                        var diaLabelId = diaGeoPsrId + "_label";
                        var diaLabelHtml = document.getElementById(diaLabelId);
                        if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null){
                            startPoint_relLabel.x = diaLabelHtml.getAttribute("x");
                            startPoint_relLabel.y = diaLabelHtml.getAttribute("y");
                        }

                        var x1_old = tarDHtml.getAttribute("x1");
                        var y1_old = tarDHtml.getAttribute("y1");
                        var x2_old = tarDHtml.getAttribute("x2");
                        var y2_old = tarDHtml.getAttribute("y2");

                        startPoint = {x: event.clientX, y: event.clientY};

                        startPoint.lineStartX1 = x1_old;
                        startPoint.lineStartY1 = y1_old;
                        startPoint.lineStartX2 = x2_old;
                        startPoint.lineStartY2 = y2_old;

                        tarDiaElement = event.target;
                        cobj=SVG(tarDiaElement);

                        fx=cobj.cx();
                        fy=cobj.cy();
                        deltax = event.clientX;
                        deltay = event.clientY;


                        //获取选中的图元所连接的虚拟连接线
                        var relVirtualLines = diaLineReMap[tarId];
                        // console.log("mousedown -- relVirtualLines: " + relVirtualLines);
                        var diaGeoPsrId = tarDHtml.getAttribute("geoPsrId");
                        // console.log("选中图元的geoPsrId为" + diaGeoPsrId);
                        // console.log(relVirtualLines.length);
                        for(var nn=0; nn<relVirtualLines.length; nn++){
                            var px;
                            var py;
                            var relVirtualLineId = relVirtualLines[nn];
                            if(relVirtualLineId == "ngpg13142311@@94e470f1-e051-4c9b-8d2f-d12a536041d3"){
                                console.log("debug");
                            }
                            // console.log("选择第" + nn + "个虚拟连接线id为" + relVirtualLineId);
                            var relVirtualLineHTML = document.getElementById(relVirtualLineId);
                            if(typeof relVirtualLineHTML != "undefined" && relVirtualLineHTML != null) {
                                // 没有拐点
                                var virLineStartGeoPsrId = relVirtualLineHTML.getAttribute("startGeoPsrId");
                                var virLineEndGeoPsrId = relVirtualLineHTML.getAttribute("endGeoPsrId");

                                // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                                if(virLineStartGeoPsrId == diaGeoPsrId){    //开始节点连接
                                    px = relVirtualLineHTML.getAttribute("x1");
                                    py = relVirtualLineHTML.getAttribute("y1");
                                } else if(virLineEndGeoPsrId == diaGeoPsrId){
                                    px = relVirtualLineHTML.getAttribute("x2");
                                    py = relVirtualLineHTML.getAttribute("y2");
                                }
                            } else {
                                // 考虑是不是有拐点的线段
                                var relVirtualLineId_0 = relVirtualLineId + ",0";
                                var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                if (relVirtualLine_0_HTML != null) {
                                    var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                    var vpSize = virtualPoint.split(";").length;
                                    var relVirtualLine_end_HTML = document.getElementById(relVirtualLineId + "," + vpSize);
                                    // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                                    var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                    var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                                    // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                    virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                    virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                    // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                                    if(virLineStartGeoPsrId == diaGeoPsrId){    //开始节点连接
                                        px = relVirtualLine_0_HTML.getAttribute("x1");
                                        py = relVirtualLine_0_HTML.getAttribute("y1");
                                    } else if(virLineEndGeoPsrId == diaGeoPsrId){
                                        px = relVirtualLine_end_HTML.getAttribute("x2");
                                        py = relVirtualLine_end_HTML.getAttribute("y2");
                                    }
                                }
                            }

                            virtualLinePointMap[relVirtualLineId] = px + "," + py;
                        }
                    }
                }
                else if(tarName.toLowerCase() == "use") {
                    // console.log("------ 点击svg use引用类型");
                    var psrId = targetElementSVG.attr('psrId');
                    var gisOId = targetElementSVG.attr('gisOId');
                    var geoPsrId = targetElementSVG.attr('geoPsrId');
                    var connection = targetElementSVG.attr('connection');
                    // console.log("tarId: " + tarId + " || psrId：" + psrId + " || geoPsrId:" + geoPsrId + " || connection:" + connection);
                    // console.log(typeof connection);
                    if(typeof connection === 'number'){
                        connection = connection + "";
                    }

                    var useType = targetElementSVG.attr('xlink:href');
                    // console.log("mousedown -- useType: " + useType);
                    if(useType == "#transformer2td" || useType == "#trans0303" || useType == "#cableTerminal" || useType == "#disconnector_1" || useType == "#tower"
                        || useType == "#dropOutFuse" || useType == "#transformer0110" || useType == "#zsBreaker" || useType == "#handSwitch"
                        || useType == "#outLinePoint" || useType == "#fac" || useType == "#mediumVPoint" || useType == "#LS" || useType == "#fuseDisc_1"
                        || useType == "#discGround_0" || useType == "#transformer0313"|| useType == "#transformer0314" || useType == "#arresterGround"
                        || useType == "#zsDisc" || useType == "#lineFaultIndicator" || useType == "#sureA" || useType == "#zsLS"
                        || useType == "#cableIntermediateJoint" || useType == "#lsoDisc" || useType == "#disc_hand_0306" || useType == "#discHand_fuse_0306"
                        || useType == "#transSingle0313" || useType == "#trans_3_2_0313" || useType == "#trans_vol_4_0314"

                        || useType == "#lineRePoint") {
                        event.stopPropagation();

                        if(useType == "#tower"){
                            let diaChild = geoPsrId2DiaMap[geoPsrId];
                            let diaSourceType = diaChild.sourceType;

                            if(diaSourceType == "uat"){
                                selectedDiaMap[tarId] = selDia(tarId, dia_uat_tower_sty);
                                // 改变点击的图元的样式
                                changeDiaStyle(tarId, dia_uat_tower_sel_sty);
                            } else {
                                if(inTyNotInUavMap != null && typeof(inTyNotInUavMap) != "undefined" && inTyNotInUavMap[geoPsrId] != null){
                                    selectedDiaMap[tarId] = selDia(tarId, dia_inTyNotInUat_tower_sty);
                                    // 改变点击的图元的样式
                                    changeDiaStyle(tarId, dia_inTyNotInUat_tower_sel_sty);
                                } else {
                                    selectedDiaMap[tarId] = selDia(tarId, dia_tower_sty);
                                    // 改变点击的图元的样式
                                    changeDiaStyle(tarId, dia_tower_sel_sty);
                                }
                            }

                            // selectedDiaMap[tarId] = selDia(tarId, dia_tower_sty);
                            // 改变点击的图元的样式
                            // changeDiaStyle(tarId, dia_tower_sel_sty);
                        } else {
                            let diaClosed = targetElementSVG.attr('closed');
                            if(diaClosed == false || diaClosed == "false"){
                                selectedDiaMap[tarId] = selDia(tarId, dia_common_cut_sty);
                                // 改变点击的图元的样式
                                changeDiaStyle(tarId, dia_common_cut_sel_sty);
                            } else {
                                selectedDiaMap[tarId] = selDia(tarId, dia_common_sty);
                                // 改变点击的图元的样式
                                changeDiaStyle(tarId, dia_common_sel_sty);
                            }
                        }

                        if (changeModel) {
                            mouseDownType = useType.substring(1, useType.length);
                            startPoint = {x: event.clientX, y: event.clientY};
                            tarDiaElement = event.target;
                            cobj=SVG(tarDiaElement);

                            fx=cobj.cx();
                            fy=cobj.cy();
                            deltax = event.clientX;
                            deltay = event.clientY;

                            var tarDHtml = document.getElementById(tarId);

                            var diaGeoPsrId = tarDHtml.getAttribute("geoPsrId");
                            // console.log("选中图元的geoPsrId为" + diaGeoPsrId);

                            var diaLabelId = diaGeoPsrId + "_label";
                            var diaLabelHtml = document.getElementById(diaLabelId);
                            if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null){
                                startPoint_relLabel.x = diaLabelHtml.getAttribute("x");
                                startPoint_relLabel.y = diaLabelHtml.getAttribute("y");
                            }

                            if(useType == "#transformer0110" || useType == "#trans0303" || useType == "#dropOutFuse" || useType == "#zsBreaker" || useType == "#tower" || useType == "#handSwitch"
                                || useType == "#outLinePoint" || useType == "#fac" || useType == "#mediumVPoint" || useType == "#transformer2td" || useType == "#LS"
                                || useType == "#fuseDisc_1"|| useType == "#discGround_0" || useType == "#disconnector_1" || useType == "#cableTerminal"
                                || useType == "#transformer0313" || useType == "#transformer0314" || useType == "#arresterGround" || useType == "#zsDisc"
                                || useType == "#lineFaultIndicator"|| useType == "#sureA" || useType == "#zsLS" || useType == "#cableIntermediateJoint"
                                || useType == "#lsoDisc" || useType == "#disc_hand_0306" || useType == "#discHand_fuse_0306" || useType == "#transSingle0313" || useType == "#trans_3_2_0313"
                                || useType == "#trans_vol_4_0314"

                                || useType == "#lineRePoint"){
                                var diaTransform = tarDHtml.getAttribute("transform");
                                var diaRotate = tarDHtml.getAttribute("rotate");
                                var xstart = diaTransform.indexOf("(");
                                var xend = diaTransform.indexOf(" ");
                                var yend = diaTransform.indexOf(")");
                                // console.log("xstart:" + xstart + " | xend:" + xend + " | yend:" + yend);

                                //获取选中的图元所连接的虚拟连接线
                                var relVirtualLines = diaLineReMap[tarId];
                                // console.log("mousedown -- 获取点击的图元所关联的虚拟连接线 relVirtualLines: " + relVirtualLines);
                                if(relVirtualLines != null && typeof relVirtualLines != undefined){
                                    for(var nn=0; nn<relVirtualLines.length; nn++){
                                        var px;
                                        var py;
                                        var relVirtualLineId = relVirtualLines[nn];
                                        // console.log("选择第" + nn + "个虚拟连接线id为" + relVirtualLineId);
                                        var relVirtualLineHTML = document.getElementById(relVirtualLineId);
                                        if(typeof relVirtualLineHTML != "undefined" && relVirtualLineHTML != null) {
                                            var virLineStartGeoPsrId = relVirtualLineHTML.getAttribute("startGeoPsrId");
                                            var virLineEndGeoPsrId = relVirtualLineHTML.getAttribute("endGeoPsrId");

                                            // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                            virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                            virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                            // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                                            if(virLineStartGeoPsrId == diaGeoPsrId){    //开始节点连接
                                                px = relVirtualLineHTML.getAttribute("x1");
                                                py = relVirtualLineHTML.getAttribute("y1");
                                            } else if(virLineEndGeoPsrId == diaGeoPsrId){
                                                px = relVirtualLineHTML.getAttribute("x2");
                                                py = relVirtualLineHTML.getAttribute("y2");
                                            }
                                            virtualLinePointMap[relVirtualLineId] = px + "," + py;
                                        } else {
                                            // 考虑是不是有拐点的线段
                                            var relVirtualLineId_0 = relVirtualLineId + ",0";
                                            var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                            if(relVirtualLine_0_HTML!= null){
                                                var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                                var vpSize = virtualPoint.split(";").length;
                                                var relVirtualLine_end_HTML = document.getElementById(relVirtualLineId + "," + vpSize);
                                                // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                                                var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                                var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                                                // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                                virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                                virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                                // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                                                if(virLineStartGeoPsrId == diaGeoPsrId){    //开始节点连接
                                                    px = relVirtualLine_0_HTML.getAttribute("x1");
                                                    py = relVirtualLine_0_HTML.getAttribute("y1");
                                                } else if(virLineEndGeoPsrId == diaGeoPsrId){
                                                    px = relVirtualLine_end_HTML.getAttribute("x2");
                                                    py = relVirtualLine_end_HTML.getAttribute("y2");
                                                }
                                                virtualLinePointMap[relVirtualLineId] = px + "," + py;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                else if(tarName.toLowerCase() == "path"){
                    // console.log("点击path");
                    var psrId = targetElementSVG.attr('psrId');
                    var gisOId = targetElementSVG.attr('gisOId');
                    var geoPsrId = targetElementSVG.attr('geoPsrId');
                    var connection = targetElementSVG.attr('connection');
                    var psrType = targetElementSVG.attr('psrType');
                    // console.log("tarId: " + tarId + " || psrId：" + psrId + " || geoPsrId:" + geoPsrId + " || connection:" + connection + " || psrType:" + psrType);
                    // zf04:开闭所     zf06:配电房   zf07 0324:环网柜     zf08:台区箱变   zf09:高分箱   zf10:用户站    0323:箱式变电站
                    if(psrType == "zf04" || psrType == "zf06" || psrType == "zf07" || psrType == "0324" || psrType == "zf08" || psrType == "zf09"
                        || psrType == "zf10" || psrType == "0323"){
                        selectedDiaMap[tarId] = selDia(tarId, lineFrame_sty);
                        // 改变点击的图元的样式
                        changeDiaStyle(tarId, lineFrame_sel_sty);

                        if (changeModel) {
                            mouseDownType = "LINEFRAME";
                            // console.log("点到开闭所等线框了...psrType:" + psrType);

                            tarDiaElement = event.target;
                            cobj=SVG(tarDiaElement);

                            var tarId = cobj.attr('id');
                            var tarDHtml = document.getElementById(tarId);
                            var diaGeoPsrId = tarDHtml.getAttribute("geoPsrId");
                            // LINEFRAME 类型 需要记录初始的pathD
                            // M10440.451416015625 2355.42529296875 L10680.451416015625 2355.42529296875 L10680.451416015625 2480.67529296875 L10440.451416015625 2480.67529296875 L10440.451416015625 2355.42529296875 Z
                            var pathD = tarDHtml.getAttribute("d");

                            startPoint = {x: event.clientX, y: event.clientY, startPathD: pathD};

                            // console.log("startPoint: " + startPoint);
                            // console.log(startPoint);

                            var diaLabelId = diaGeoPsrId + "_label";
                            var diaLabelHtml = document.getElementById(diaLabelId);
                            if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null){
                                startPoint_relLabel.x = diaLabelHtml.getAttribute("x");
                                startPoint_relLabel.y = diaLabelHtml.getAttribute("y");
                            }
                        }
                    }
                }
                else if(tarName.toLowerCase() == "text" || tarName.toLowerCase() == "tspan"){
                    if (changeModel) {
                        if(tarName.toLowerCase() == "tspan"){
                            // console.log("------ 点击tspan 类型");
                            // console.log(targetElement.parentNode);
                            var tParentNode = targetElement.parentNode;

                            var targetElementSVG_parant = SVG.adopt(tParentNode);
                            var textId = targetElementSVG_parant.attr('id');
                            var geoPsrId = targetElementSVG_parant.attr('diaGeoPsrId');
                            // console.log("textId: " + textId + " || geoPsrId：" + geoPsrId);
                            event.stopPropagation();
                            mouseDownType = "label_tspan";
                            startPoint = {x: event.clientX, y: event.clientY};
                            cobj=SVG(tParentNode);

                            fx=cobj.cx();
                            fy=cobj.cy();
                            deltax = event.clientX;
                            deltay = event.clientY;

                            // console.log("mousedown -- " + mouseDownType + " -- (fx, fy):(" + fx + " ," + fy + ") -- (deltax, deltay):(" + deltax + ", " + deltay + ")");

                            var tarId = cobj.attr('id');
                            // console.log("tarId: " + tarId);
                            selectedPelId = tarId;

                        }
                        else {
                            // console.log("------ 点击text 标签类型");

                            var textId = targetElementSVG.attr('id');
                            var geoPsrId = targetElementSVG.attr('diaGeoPsrId');
                            // console.log("textId: " + textId + " || geoPsrId：" + geoPsrId);
                            event.stopPropagation();
                            mouseDownType = "label";
                            startPoint = {x: event.clientX, y: event.clientY};
                            tarDiaElement = event.target;
                            cobj=SVG(tarDiaElement);

                            fx=cobj.cx();
                            fy=cobj.cy();
                            deltax = event.clientX;
                            deltay = event.clientY;

                            // console.log("mousedown -- " + mouseDownType + " -- (fx, fy):(" + fx + " ," + fy + ") -- (deltax, deltay):(" + deltax + ", " + deltay + ")");

                            var tarId = cobj.attr('id');
                            // console.log("tarId: " + tarId);
                            selectedPelId = tarId;
                        }
                    }
                }
                else if(tarName.toLowerCase() == "circle"){
                    var cType = targetElementSVG.attr('type');
                    if(cType == "virtualPoint"){
                        selectedDiaMap[tarId] = selDia(tarId, virtualNode_sty);
                        // 改变点击的图元的样式
                        changeDiaStyle(tarId, virtualNode_sel_sty);

                        if (changeModel) {
                            // console.log("点击拐点");
                            var tarId = targetElementSVG.attr('id');
                            var nStart = targetElementSVG.attr('start');
                            var nEnd = targetElementSVG.attr('end');
                            mouseDownType = "virtualPoint";
                            startPoint = {x: event.clientX, y: event.clientY};
                            tarDiaElement = event.target;
                            cobj=SVG(tarDiaElement);

                            fx=cobj.cx();
                            fy=cobj.cy();
                            deltax = event.clientX;
                            deltay = event.clientY;
                        }
                    }
                    else if(cType == "busPoint") {
                        // 点击母线连接点
                        // console.log("点击母线连接点");
                        selectedDiaMap[tarId] = selDia(tarId, busPoint_sty);
                        // 改变点击的图元的样式
                        changeDiaStyle(tarId, busPoint_sel_sty);

                        if(tarId == "8824218e-08c3-497a-9e78-b85bb3612a55" || tarId == "09908b4c-4dbb-4ea0-833c-c5e1423c01db"){
                            console.log("debug");
                        }

                        if (changeModel) {
                            mouseDownType = "busPoint";
                            startPoint = {x: event.clientX, y: event.clientY};
                            tarDiaElement = event.target;
                            cobj=SVG(tarDiaElement);

                            fx=cobj.cx();
                            fy=cobj.cy();
                            deltax = event.clientX;
                            deltay = event.clientY;
                        }
                    }
                }
                else {
                    // console.log("点击其他");
                    // console.log(tarName.toLowerCase());
                }
            }
        }
    });

    $("#drawPdSvg").bind("mousemove", function(event){
        // console.log("mousemove ---- mouseDownType + " + mouseDownType + " ---- (" + event.clientX + "," + event.clientY + ")");

        if (changeModel) {
            // console.log("mousemove --- mouseDownType: " + mouseDownType);
            var targetElement=event.target;

            if(mouseDownType == "transformer2td" || mouseDownType == "trans0303" || mouseDownType == "cableTerminal" || mouseDownType == "disconnector_1" || mouseDownType == "tower"
                || mouseDownType == "dropOutFuse" || mouseDownType == "transformer0110" || mouseDownType == "zsBreaker" || mouseDownType == "handSwitch"
                || mouseDownType == "导线段" || mouseDownType == "连接线" || mouseDownType == "outLinePoint" || mouseDownType == "fac"
                || mouseDownType == "mediumVPoint" || mouseDownType == "LS" || mouseDownType == "fuseDisc_1" || mouseDownType == "discGround_0"
                || mouseDownType == "transformer0313" || mouseDownType == "transformer0314" || mouseDownType == "arresterGround"
                || mouseDownType == "母线" || mouseDownType == "zsDisc" || mouseDownType == "lineFaultIndicator" || mouseDownType == "sureA"
                || mouseDownType == "zsLS" || mouseDownType == "cableIntermediateJoint" || mouseDownType == "lsoDisc" || mouseDownType == "disc_hand_0306"
                || mouseDownType == "discHand_fuse_0306" || mouseDownType == "transSingle0313" || mouseDownType == "trans_3_2_0313" || mouseDownType == "trans_vol_4_0314"

                || mouseDownType == "lineRePoint"){
                // console.log("mousemove -- mouseDownType： " + mouseDownType + "---------------------------------- mousemove");

                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                var diaGeoPsrId = tarDHtml.getAttribute("geoPsrId");

                var rotate = tarDHtml.getAttribute("rotate");
                // console.log("rotate:" + rotate);

                // 计算新的坐标
                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;

                // console.log("dx:" + dx + " || dy:" + dy);
                var directionMap = {
                    0: { x: dx, y: dy },
                    90: { x: dy, y: 0 - parseFloat(dx)},
                    180: { x: 0 - parseFloat(dx), y: 0 - parseFloat(dy)},
                    270: { x: 0 - parseFloat(dy), y: dx }
                };

                var rotateAngle = 0;
                // 获取当前旋转角度对应的映射
                if(mouseDownType == "导线段" || mouseDownType == "连接线" || mouseDownType == "母线" || mouseDownType == "lineRePoint"){

                } else {
                    rotateAngle = cobj.attr("transform").split("rotate(")[1].split(")")[0];
                }
                // console.log("rotateAngle:" + rotateAngle);
                rotateAngle = dealRotate(rotateAngle);
                // console.log("deal rotateAngle:" + rotateAngle);
                var direction = directionMap[parseInt(rotateAngle)];

                // --- 处理图元标签随图元拖动而联动 ---
                var diaLabelId = diaGeoPsrId + "_label";
                var diaLabelHtml = document.getElementById(diaLabelId);

                var label_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                var label_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);

                if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                    var dhcl = 0;
                    if(diaLabelHtml.children == null || typeof(diaLabelHtml.children) == "undefined"){
                        dhcl = 0;
                    } else {
                        dhcl = diaLabelHtml.children.length;
                    }

                    /*var dhcl = diaLabelHtml.children.length;
                    if(dhcl == undefined || dhcl == null || typeof dhcl == "undefined"){
                        dhcl = 0;
                    }*/

                    if (diaLabelHtml.children != null && typeof(diaLabelHtml.children) != "undefined" && diaLabelHtml.children.length > 0) {
                        // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                        // 所有孩子节点，移动
                        for (let i = 0; i < diaLabelHtml.children.length; i++) {
                            const childNode = diaLabelHtml.children[i];
                            if (childNode.nodeName.toLowerCase() === 'tspan') {
                                // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                // 更新x坐标
                                childNode.setAttribute('x', label_newx);
                                // childNode.setAttribute('y', parseFloat(label_newy) - (diaLabelHtml.children.length - i) * 10 + 20);
                                childNode.setAttribute('y', parseFloat(label_newy) + i*labelWrapHeight);
                                // childNode.setAttribute("y", parseFloat(label_newy) + i * 10);
                            } else {
                                // 不是tspan节点，按需处理或忽略
                            }
                        }
                        diaLabelHtml.setAttribute("x", label_newx);
                        diaLabelHtml.setAttribute("y", label_newy);
                    } else {
                        if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                            // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                            diaLabelHtml.setAttribute("x", label_newx);
                            diaLabelHtml.setAttribute("y", label_newy);
                        }
                    }
                }

                let pExists = false;
                let qExists = false;
                // --- 处理图元量测标签随图元拖动而联动 ---
                var diaMeasPId = diaGeoPsrId + "_meas_p";
                var diaMeasPHtml = document.getElementById(diaMeasPId);
                if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                    pExists = true;
                    // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                    var measP_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                    var measP_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                    diaMeasPHtml.setAttribute("x", measP_newx);
                    diaMeasPHtml.setAttribute("y", parseFloat(measP_newy) + dhcl * labelWrapHeight);
                }
                var diaMeasQId = diaGeoPsrId + "_meas_q";
                var diaMeasQHtml = document.getElementById(diaMeasQId);
                if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                    qExists = true;
                    // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                    var measQ_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                    var measQ_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                    diaMeasQHtml.setAttribute("x", measQ_newx);
                    diaMeasQHtml.setAttribute("y", parseFloat(measQ_newy) + dhcl * labelWrapHeight + labelWrapHeight);
                }

                var diaVMPUId = diaGeoPsrId + "_vm_pu";
                var diaVMPUHtml = document.getElementById(diaVMPUId);
                if(typeof diaVMPUHtml != "undefined" && diaVMPUHtml != null) {
                    // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                    var VMPU_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                    var VMPU_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                    diaVMPUHtml.setAttribute("x", VMPU_newx);
                    let finalY;
                    if(pExists){
                        finalY = parseFloat(VMPU_newy) + dhcl * labelWrapHeight + labelWrapHeight * 2;
                    } else {
                        finalY = parseFloat(VMPU_newy) + dhcl * labelWrapHeight;
                    }
                    diaVMPUHtml.setAttribute("y", finalY);
                }
                var diaVAId = diaGeoPsrId + "_v_a";
                var diaVAHtml = document.getElementById(diaVAId);
                if(typeof diaVAHtml != "undefined" && diaVAHtml != null) {
                    // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                    var VA_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                    var VA_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                    diaVAHtml.setAttribute("x", VA_newx);
                    let finalY;
                    if(pExists){
                        finalY = parseFloat(VA_newy) + dhcl * labelWrapHeight + labelWrapHeight * 3;
                    } else {
                        finalY = parseFloat(VA_newy) + dhcl * labelWrapHeight + labelWrapHeight;
                    }
                    diaVAHtml.setAttribute("y", finalY);
                }

                // 计算新的坐标
                var newX = parseFloat(fx) + parseFloat(direction.x);
                var newY = parseFloat(fy) + parseFloat(direction.y);

                // const newX = fx + dx;
                // const newY = fy + dy;

                cobj.cx(newX);
                cobj.cy(newY);

                // console.log("move - newX:" + newX + " || newY: " + newY);


                // console.log("tarId:" + tarId);
                // console.log(diaLineReMap);
                var relVirtualLines = diaLineReMap[tarId];
                // console.log("mousemove -- relVirtualLines: " + relVirtualLines);
                if(relVirtualLines != null && typeof relVirtualLines != undefined) {
                    for (var i = 0; i < relVirtualLines.length; i++) {
                        var virtualLineId = relVirtualLines[i];
                        // console.log("当前处理:" + virtualLineId);

                        var virLinePointXY = virtualLinePointMap[virtualLineId];
                        if(virLinePointXY == null || typeof virLinePointXY == undefined){
                            continue;
                        }
                        var vlx = virLinePointXY.split(',')[0];
                        var vly = virLinePointXY.split(',')[1];
                        const newX1 = (parseFloat(vlx) + parseFloat(dx));
                        const newY1 = (parseFloat(vly) + parseFloat(dy));

                        var virtualLineHtml = document.getElementById(virtualLineId);

                        if(typeof virtualLineHtml != "undefined" && virtualLineHtml != null) {
                            var virLineStartGeoPsrId = virtualLineHtml.getAttribute("startGeoPsrId");
                            var virLineEndGeoPsrId = virtualLineHtml.getAttribute("endGeoPsrId");

                            // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                            virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                            virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                            // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                            if(virLineStartGeoPsrId == diaGeoPsrId){    //开始节点连接
                                virtualLineHtml.setAttribute("x1", newX1);
                                virtualLineHtml.setAttribute("y1", newY1);
                            } else if(virLineEndGeoPsrId == diaGeoPsrId){
                                virtualLineHtml.setAttribute("x2", newX1);
                                virtualLineHtml.setAttribute("y2", newY1);
                            }

                            // -------- 处理线路标签 ---------
                            var lineLabelId = virtualLineId + "_label";
                            var lineLabelHtml = document.getElementById(lineLabelId);
                            let x1_old, y1_old, x2_old, y2_old;
                            if (virLineStartGeoPsrId == diaGeoPsrId) {    // 开始节点连接
                                x2_old = parseFloat(virtualLineHtml.getAttribute("x2"));
                                y2_old = parseFloat(virtualLineHtml.getAttribute("y2"));
                                x1_old = parseFloat(newX1);
                                y1_old = parseFloat(newY1);
                            } else if (virLineEndGeoPsrId == diaGeoPsrId) {
                                x1_old = parseFloat(virtualLineHtml.getAttribute("x1"));
                                y1_old = parseFloat(virtualLineHtml.getAttribute("y1"));
                                x2_old = parseFloat(newX1);
                                y2_old = parseFloat(newY1);
                            }

                            // 计算线路角度
                            // 计算方向向量
                            let dx_lineLabel = x2_old - x1_old;
                            let dy_lineLabel = y2_old - y1_old;
                            // 计算标签旋转角度（弧度），并转换为角度。
                            let angle_label = Math.atan2(dy_lineLabel, dx_lineLabel) * (180 / Math.PI);

                            // 计算标签的中心点
                            let centerX = (x1_old + x2_old) / 2;
                            let centerY = (y1_old + y2_old) / 2;

                            // 计算标签到中心点的距离
                            let distanceFromCenter = 15; // 固定距离，可以根据需要调整

                            // 计算标签的新位置
                            let offsetX = distanceFromCenter * Math.cos(angle_label * Math.PI / 180 + Math.PI / 2);
                            let offsetY = distanceFromCenter * Math.sin(angle_label * Math.PI / 180 + Math.PI / 2);

                            let newX = centerX + offsetX;
                            let newY = centerY + offsetY;

                            // 设置标签元素的位置和旋转属性
                            lineLabelHtml.setAttribute("x", newX);
                            lineLabelHtml.setAttribute("y", newY);
                            lineLabelHtml.setAttribute("transform", `rotate(${angle_label}, ${newX}, ${newY})`);
                        }
                        else {
                            // 考虑是不是有拐点的线段
                            var relVirtualLineId_0 = virtualLineId + ",0";
                            var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                            if(relVirtualLine_0_HTML!= null){
                                var virtualPointStr = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                var vpSize = virtualPointStr.split(";").length;
                                var relVirtualLine_end_HTML = document.getElementById(virtualLineId + "," + vpSize);
                                // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                                var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                                // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                                if(virLineStartGeoPsrId == diaGeoPsrId){    //开始节点连接
                                    console.log("连接在带拐点的线的开始段");
                                    relVirtualLine_0_HTML.setAttribute("x1", newX1);
                                    relVirtualLine_0_HTML.setAttribute("y1", newY1);

                                    // 处理线路标签
                                    var diaLabelId = virtualLineId + "_label";
                                    var lineLabelHtml = document.getElementById(diaLabelId);
                                    let x1_old, y1_old, x2_old, y2_old;

                                    x2_old = parseFloat(relVirtualLine_0_HTML.getAttribute("x2"));
                                    y2_old = parseFloat(relVirtualLine_0_HTML.getAttribute("y2"));
                                    x1_old = parseFloat(newX1);
                                    y1_old = parseFloat(newY1);

                                    // 计算线路角度
                                    // 计算方向向量
                                    let dx_lineLabel = x2_old - x1_old;
                                    let dy_lineLabel = y2_old - y1_old;
                                    // 计算标签旋转角度（弧度），并转换为角度。
                                    let angle_label = Math.atan2(dy_lineLabel, dx_lineLabel) * (180 / Math.PI);

                                    // 计算标签的中心点
                                    let centerX = (x1_old + x2_old) / 2;
                                    let centerY = (y1_old + y2_old) / 2;

                                    // 计算标签到中心点的距离
                                    let distanceFromCenter = 15; // 固定距离，可以根据需要调整

                                    // 计算标签的新位置
                                    let offsetX = distanceFromCenter * Math.cos(angle_label * Math.PI / 180 + Math.PI / 2);
                                    let offsetY = distanceFromCenter * Math.sin(angle_label * Math.PI / 180 + Math.PI / 2);

                                    let newX = centerX + offsetX;
                                    let newY = centerY + offsetY;

                                    // 设置标签元素的位置和旋转属性
                                    lineLabelHtml.setAttribute("x", newX);
                                    lineLabelHtml.setAttribute("y", newY);
                                    lineLabelHtml.setAttribute("transform", `rotate(${angle_label}, ${newX}, ${newY})`);
                                } else if(virLineEndGeoPsrId == diaGeoPsrId){
                                    console.log("连接在带拐点的线的结束段");
                                    relVirtualLine_end_HTML.setAttribute("x2", newX1);
                                    relVirtualLine_end_HTML.setAttribute("y2", newY1);

                                    // 结束节点相连时，不需要调整线路标签坐标
                                }
                            }
                        }

                        if(mouseDownType == "母线"){
                            //知道拖动母线时带动的虚拟连接线的ID
                            //通过虚拟连接线ID,找到与母线连接的点,将点的圆心也设置为newX1和newY1
                            // console.log("virtualLineId:" + virtualLineId);
                            var mpId = virtualLineId + "&" + diaGeoPsrId;
                            var busLineConnPoint = virLine2BusPointMap[mpId];
                            // console.log("mpId: " + mpId);
                            var busLineConnPointId = busLineConnPoint.id;
                            var busLineConnPointHtml = document.getElementById(busLineConnPointId);
                            busLineConnPointHtml.setAttribute("cx", newX1);
                            busLineConnPointHtml.setAttribute("cy", newY1);
                        }
                    }
                }

            }
            else if(mouseDownType == "label"){
                console.log("拖动label");
                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                // 计算新的坐标
                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;
                // console.log("dx:" + dx + " || dy:" + dy);

                const newX = fx + eval(dx);
                const newY = fy + eval(dy);

                cobj.cx(newX);
                cobj.cy(newY);

                if(tarId.endsWith("_label")){
                    var diaGeoPsrId_l = tarId.split("_")[0];
                    // --- 处理图元量测标签随图元拖动而联动 ---
                    var diaMeasPId = diaGeoPsrId_l + "_meas_p";
                    var diaMeasPHtml = document.getElementById(diaMeasPId);
                    if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var measP_newx = parseFloat(fx) + parseFloat(dx);
                        var measP_newy = parseFloat(fy) + parseFloat(dy);
                        diaMeasPHtml.setAttribute("x", measP_newx);
                        diaMeasPHtml.setAttribute("y", parseFloat(measP_newy) + labelWrapHeight);
                    }
                    var diaMeasQId = diaGeoPsrId_l + "_meas_q";
                    var diaMeasQHtml = document.getElementById(diaMeasQId);
                    if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var measQ_newx = parseFloat(fx) + parseFloat(dx);
                        var measQ_newy = parseFloat(fy) + parseFloat(dy);
                        diaMeasQHtml.setAttribute("x", measQ_newx);
                        diaMeasQHtml.setAttribute("y", parseFloat(measQ_newy) + labelWrapHeight);
                    }

                    var diaVMPUId = diaGeoPsrId_l + "_vm_pu";
                    var diaVMPUHtml = document.getElementById(diaVMPUId);
                    if(typeof diaVMPUHtml != "undefined" && diaVMPUHtml != null) {
                        var VMPU_newx = parseFloat(fx) + parseFloat(dx);
                        var VMPU_newy = parseFloat(fy) + parseFloat(dy);
                        diaVMPUHtml.setAttribute("x", VMPU_newx);
                        diaVMPUHtml.setAttribute("y", parseFloat(VMPU_newy) + labelWrapHeight);
                    }
                    var diaVAId = diaGeoPsrId_l + "_v_a";
                    var diaVAHtml = document.getElementById(diaVAId);
                    if(typeof diaVAHtml != "undefined" && diaVAHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var VA_newx = parseFloat(fx) + parseFloat(dx);
                        var VA_newy = parseFloat(fy) + parseFloat(dy);
                        diaVAHtml.setAttribute("x", VA_newx);
                        diaVAHtml.setAttribute("y", parseFloat(VA_newy) + labelWrapHeight);
                    }
                }
                else if(tarId.endsWith("_meas_p")){
                    var diaGeoPsrId_l = tarId.split("_")[0];
                    // --- 处理图元量测标签随图元拖动而联动 ---
                    var diaLabelId = diaGeoPsrId_l + "_label";
                    var diaLabelHtml = document.getElementById(diaLabelId);

                    if (diaLabelHtml.children.length > 0) {
                        // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                        // 所有孩子节点，移动
                        for (let i = 0; i < diaLabelHtml.children.length; i++) {
                            const childNode = diaLabelHtml.children[i];
                            if (childNode.nodeName.toLowerCase() === 'tspan') {
                                // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                // 更新x坐标
                                childNode.setAttribute('x', newX);
                                childNode.setAttribute('y', parseFloat(newY) - (diaLabelHtml.children.length - i) * labelWrapHeight);
                            } else {
                                // 不是tspan节点，按需处理或忽略
                            }
                        }

                        diaLabelHtml.setAttribute("x", newX);
                        diaLabelHtml.setAttribute("y", newY - labelWrapHeight * 2);
                    } else {
                        if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                            var label_newx = parseFloat(fx) + parseFloat(dx);
                            var label_newy = parseFloat(fy) + parseFloat(dy);
                            diaLabelHtml.setAttribute("x", label_newx);
                            diaLabelHtml.setAttribute("y", parseFloat(label_newy) - labelWrapHeight * 2);
                        }
                    }

                    // --- 处理Q
                    var diaMeasQId = diaGeoPsrId_l + "_meas_q";
                    var diaMeasQHtml = document.getElementById(diaMeasQId);
                    if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                        var measQ_newx = parseFloat(fx) + parseFloat(dx);
                        var measQ_newy = parseFloat(fy) + parseFloat(dy);
                        diaMeasQHtml.setAttribute("x", measQ_newx);
                        diaMeasQHtml.setAttribute("y", parseFloat(measQ_newy) + labelWrapHeight);
                    }

                    var diaVMPUId = diaGeoPsrId_l + "_vm_pu";
                    var diaVMPUHtml = document.getElementById(diaVMPUId);
                    if(typeof diaVMPUHtml != "undefined" && diaVMPUHtml != null) {
                        var VMPU_newx = parseFloat(fx) + parseFloat(dx);
                        var VMPU_newy = parseFloat(fy) + parseFloat(dy);
                        diaVMPUHtml.setAttribute("x", VMPU_newx);
                        diaVMPUHtml.setAttribute("y", parseFloat(VMPU_newy) + labelWrapHeight * 2);
                    }
                    var diaVAId = diaGeoPsrId_l + "_v_a";
                    var diaVAHtml = document.getElementById(diaVAId);
                    if(typeof diaVAHtml != "undefined" && diaVAHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var VA_newx = parseFloat(fx) + parseFloat(dx);
                        var VA_newy = parseFloat(fy) + parseFloat(dy);
                        diaVAHtml.setAttribute("x", VA_newx);
                        diaVAHtml.setAttribute("y", parseFloat(VA_newy) + labelWrapHeight * 3);
                    }
                }
                else if(tarId.endsWith("_meas_q")){
                    var diaGeoPsrId_l = tarId.split("_")[0];
                    // --- 处理图元量测标签随图元拖动而联动 ---
                    var diaLabelId = diaGeoPsrId_l + "_label";
                    var diaLabelHtml = document.getElementById(diaLabelId);

                    if (diaLabelHtml.children.length > 0) {
                        // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                        // 所有孩子节点，移动
                        for (let i = 0; i < diaLabelHtml.children.length; i++) {
                            const childNode = diaLabelHtml.children[i];
                            if (childNode.nodeName.toLowerCase() === 'tspan') {
                                // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                // 更新x坐标
                                childNode.setAttribute('x', newX);
                                childNode.setAttribute('y', parseFloat(newY) - (diaLabelHtml.children.length - i) * labelWrapHeight - labelWrapHeight);
                            } else {
                                // 不是tspan节点，按需处理或忽略
                            }
                        }
                        diaLabelHtml.setAttribute("x", newX);
                        diaLabelHtml.setAttribute("y", newY - labelWrapHeight * 3);
                    } else {
                        if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                            var label_newx = parseFloat(fx) + parseFloat(dx);
                            var label_newy = parseFloat(fy) + parseFloat(dy);
                            diaLabelHtml.setAttribute("x", label_newx);
                            diaLabelHtml.setAttribute("y", parseFloat(label_newy) - labelWrapHeight * 3);
                        }
                    }


                    // --- 处理P
                    var diaMeasPId = diaGeoPsrId_l + "_meas_p";
                    var diaMeasPHtml = document.getElementById(diaMeasPId);
                    if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                        var measP_newx = parseFloat(fx) + parseFloat(dx);
                        var measP_newy = parseFloat(fy) + parseFloat(dy);
                        diaMeasPHtml.setAttribute("x", measP_newx);
                        diaMeasPHtml.setAttribute("y", parseFloat(measP_newy) - labelWrapHeight);
                    }

                    var diaVMPUId = diaGeoPsrId_l + "_vm_pu";
                    var diaVMPUHtml = document.getElementById(diaVMPUId);
                    if(typeof diaVMPUHtml != "undefined" && diaVMPUHtml != null) {
                        var VMPU_newx = parseFloat(fx) + parseFloat(dx);
                        var VMPU_newy = parseFloat(fy) + parseFloat(dy);
                        diaVMPUHtml.setAttribute("x", VMPU_newx);
                        diaVMPUHtml.setAttribute("y", parseFloat(VMPU_newy) + labelWrapHeight);
                    }
                    var diaVAId = diaGeoPsrId_l + "_v_a";
                    var diaVAHtml = document.getElementById(diaVAId);
                    if(typeof diaVAHtml != "undefined" && diaVAHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var VA_newx = parseFloat(fx) + parseFloat(dx);
                        var VA_newy = parseFloat(fy) + parseFloat(dy);
                        diaVAHtml.setAttribute("x", VA_newx);
                        diaVAHtml.setAttribute("y", parseFloat(VA_newy) + labelWrapHeight * 2);
                    }
                }
                else if(tarId.endsWith("_vm_pu")){
                    var diaGeoPsrId_l = tarId.split("_")[0];
                    // --- 处理图元量测标签随图元拖动而联动 ---
                    var diaLabelId = diaGeoPsrId_l + "_label";
                    var diaLabelHtml = document.getElementById(diaLabelId);

                    let pExists = false;
                    var diaMeasPId = diaGeoPsrId_l + "_meas_p";
                    var diaMeasPHtml = document.getElementById(diaMeasPId);
                    if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                        pExists = true;
                    }

                    if (diaLabelHtml.children.length > 0) {
                        // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                        // 所有孩子节点，移动
                        for (let i = 0; i < diaLabelHtml.children.length; i++) {
                            const childNode = diaLabelHtml.children[i];
                            if (childNode.nodeName.toLowerCase() === 'tspan') {
                                // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                // 更新x坐标
                                childNode.setAttribute('x', newX);
                                let finalY;
                                if(pExists){
                                    finalY = parseFloat(newY) - (diaLabelHtml.children.length - i) * labelWrapHeight - labelWrapHeight*2;
                                } else {
                                    finalY = parseFloat(newY) - (diaLabelHtml.children.length - i) * labelWrapHeight;
                                }
                                childNode.setAttribute('y', finalY);
                            } else {
                                // 不是tspan节点，按需处理或忽略
                            }
                        }
                        diaLabelHtml.setAttribute("x", newX);
                        diaLabelHtml.setAttribute("y", pExists ? (newY - labelWrapHeight * 4) : (newY - labelWrapHeight * 1));
                    } else {
                        if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                            var label_newx = parseFloat(fx) + parseFloat(dx);
                            var label_newy = parseFloat(fy) + parseFloat(dy);
                            diaLabelHtml.setAttribute("x", label_newx);
                            diaLabelHtml.setAttribute("y", parseFloat(label_newy) - labelWrapHeight * 4);
                        }
                    }

                    // --- 处理P
                    if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                        var measP_newx = parseFloat(fx) + parseFloat(dx);
                        var measP_newy = parseFloat(fy) + parseFloat(dy);
                        diaMeasPHtml.setAttribute("x", measP_newx);
                        diaMeasPHtml.setAttribute("y", parseFloat(measP_newy) - labelWrapHeight * 2);
                    }

                    // --- 处理Q
                    var diaMeasQId = diaGeoPsrId_l + "_meas_q";
                    var diaMeasQHtml = document.getElementById(diaMeasQId);
                    if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                        var measQ_newx = parseFloat(fx) + parseFloat(dx);
                        var measQ_newy = parseFloat(fy) + parseFloat(dy);
                        diaMeasQHtml.setAttribute("x", measQ_newx);
                        diaMeasQHtml.setAttribute("y", parseFloat(measQ_newy) - labelWrapHeight * 1);
                    }

                    var diaVAId = diaGeoPsrId_l + "_v_a";
                    var diaVAHtml = document.getElementById(diaVAId);
                    if(typeof diaVAHtml != "undefined" && diaVAHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var VA_newx = parseFloat(fx) + parseFloat(dx);
                        var VA_newy = parseFloat(fy) + parseFloat(dy);
                        diaVAHtml.setAttribute("x", VA_newx);
                        let finalY;
                        if(pExists){
                            finalY = parseFloat(VA_newy) + labelWrapHeight * 1;
                        } else {
                            finalY = parseFloat(VA_newy) + labelWrapHeight * 1;
                        }
                        diaVAHtml.setAttribute("y", finalY);
                    }
                }
                else if(tarId.endsWith("_v_a")){
                    var diaGeoPsrId_l = tarId.split("_")[0];
                    // --- 处理图元量测标签随图元拖动而联动 ---
                    var diaLabelId = diaGeoPsrId_l + "_label";
                    var diaLabelHtml = document.getElementById(diaLabelId);

                    let pExists = false;
                    var diaMeasPId = diaGeoPsrId_l + "_meas_p";
                    var diaMeasPHtml = document.getElementById(diaMeasPId);
                    if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                        pExists = true;
                    }

                    if (diaLabelHtml.children.length > 0) {
                        // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                        // 所有孩子节点，移动
                        for (let i = 0; i < diaLabelHtml.children.length; i++) {
                            const childNode = diaLabelHtml.children[i];
                            if (childNode.nodeName.toLowerCase() === 'tspan') {
                                // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                // 更新x坐标
                                childNode.setAttribute('x', newX);
                                let finalY;
                                if(pExists){
                                    finalY = parseFloat(newY) - (diaLabelHtml.children.length - i) * labelWrapHeight - labelWrapHeight*3;
                                } else {
                                    finalY = parseFloat(newY) - (diaLabelHtml.children.length - i) * labelWrapHeight - labelWrapHeight*1;
                                }
                                childNode.setAttribute('y', finalY);
                            } else {
                                // 不是tspan节点，按需处理或忽略
                            }
                        }
                        diaLabelHtml.setAttribute("x", newX);
                        diaLabelHtml.setAttribute("y", pExists ? (newY - labelWrapHeight * 5) : (newY - labelWrapHeight * 2));
                    } else {
                        if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                            var label_newx = parseFloat(fx) + parseFloat(dx);
                            var label_newy = parseFloat(fy) + parseFloat(dy);
                            diaLabelHtml.setAttribute("x", label_newx);
                            diaLabelHtml.setAttribute("y", parseFloat(label_newy) - labelWrapHeight * 5);
                        }
                    }

                    // --- 处理P
                    if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                        var measP_newx = parseFloat(fx) + parseFloat(dx);
                        var measP_newy = parseFloat(fy) + parseFloat(dy);
                        diaMeasPHtml.setAttribute("x", measP_newx);
                        diaMeasPHtml.setAttribute("y", parseFloat(measP_newy) - labelWrapHeight * 3);
                    }

                    // --- 处理Q
                    var diaMeasQId = diaGeoPsrId_l + "_meas_q";
                    var diaMeasQHtml = document.getElementById(diaMeasQId);
                    if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                        var measQ_newx = parseFloat(fx) + parseFloat(dx);
                        var measQ_newy = parseFloat(fy) + parseFloat(dy);
                        diaMeasQHtml.setAttribute("x", measQ_newx);
                        diaMeasQHtml.setAttribute("y", parseFloat(measQ_newy) - labelWrapHeight * 2);
                    }

                    var diaVMPUId = diaGeoPsrId_l + "_vm_pu";
                    var diaVMPUHtml = document.getElementById(diaVMPUId);
                    if(typeof diaVMPUHtml != "undefined" && diaVMPUHtml != null) {
                        var VMPU_newx = parseFloat(fx) + parseFloat(dx);
                        var VMPU_newy = parseFloat(fy) + parseFloat(dy);
                        diaVMPUHtml.setAttribute("x", VMPU_newx);
                        let finalY;
                        if(pExists){
                            finalY = parseFloat(VMPU_newy) - labelWrapHeight * 1;
                        } else {
                            finalY = parseFloat(VMPU_newy) - labelWrapHeight * 1;
                        }
                        diaVMPUHtml.setAttribute("y", finalY);
                    }
                }
                // console.log("move - newX:" + newX + " || newY: " + newY);
            }
            else if(mouseDownType == "label_tspan"){
                console.log("移动 tspan");
                var tarId = cobj.attr('id');
                // console.log(tarId);
                var tarDHtml = document.getElementById(tarId);

                // 计算新的坐标
                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;

                var newX = fx + eval(dx);
                var newY = fy + eval(dy);
                // console.log("newX:" + newX + " || newY: " + newY);
                // console.log("fx:" + fx + " || fy:" + fy + " || dx:" + dx + " || dy:" + dy);

                cobj.cx(newX);
                cobj.cy(newY);
                // console.log(cobj);

                tarDHtml.setAttribute("x", newX);
                tarDHtml.setAttribute("y", newY);


                if (cobj.node.childNodes.length > 0) {
                    // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                    // 所有孩子节点，移动
                    for (let i = 0; i < cobj.node.childNodes.length; i++) {
                        const childNode = cobj.node.childNodes[i];
                        if (childNode.nodeName.toLowerCase() === 'tspan') {
                            // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                            // 更新x坐标
                            childNode.setAttribute('x', newX);
                            childNode.setAttribute('y', parseFloat(newY) + i * labelWrapHeight);
                        } else {
                            // 不是tspan节点，按需处理或忽略
                        }
                    }
                }

                var measPY = parseFloat(newY) + cobj.node.childNodes.length * labelWrapHeight;
                let pExists = false;
                if(tarId.endsWith("_label")){
                    var diaGeoPsrId_l = tarId.split("_")[0];
                    // --- 处理图元量测标签随图元拖动而联动 ---
                    var diaMeasPId = diaGeoPsrId_l + "_meas_p";
                    var diaMeasPHtml = document.getElementById(diaMeasPId);
                    if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                        pExists = true;
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var measP_newx = parseFloat(fx) + parseFloat(dx);
                        diaMeasPHtml.setAttribute("x", measP_newx);
                        diaMeasPHtml.setAttribute("y", measPY);
                    }
                    var diaMeasQId = diaGeoPsrId_l + "_meas_q";
                    var diaMeasQHtml = document.getElementById(diaMeasQId);
                    if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var measQ_newx = parseFloat(fx) + parseFloat(dx);
                        diaMeasQHtml.setAttribute("x", measQ_newx);
                        diaMeasQHtml.setAttribute("y", parseFloat(measPY) + labelWrapHeight);
                    }

                    var diaVMPUId = diaGeoPsrId_l + "_vm_pu";
                    var diaVMPUHtml = document.getElementById(diaVMPUId);
                    if(typeof diaVMPUHtml != "undefined" && diaVMPUHtml != null) {
                        var VMPU_newx = parseFloat(fx) + parseFloat(dx);
                        diaVMPUHtml.setAttribute("x", VMPU_newx);
                        let finalY;
                        if(pExists){
                            finalY = parseFloat(measPY) + labelWrapHeight * 2;
                        } else {
                            finalY = parseFloat(measPY);
                        }
                        diaVMPUHtml.setAttribute("y", finalY);
                    }
                    var diaVAId = diaGeoPsrId_l + "_v_a";
                    var diaVAHtml = document.getElementById(diaVAId);
                    if(typeof diaVAHtml != "undefined" && diaVAHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        var VA_newx = parseFloat(fx) + parseFloat(dx);
                        diaVAHtml.setAttribute("x", VA_newx);
                        let finalY;
                        if(pExists){
                            finalY = parseFloat(measPY) + labelWrapHeight * 3;
                        } else {
                            finalY = parseFloat(measPY) + labelWrapHeight * 1;
                        }
                        diaVAHtml.setAttribute("y", finalY);
                    }
                }
            }
            else if(mouseDownType == "black"){
                zoomDrag = true;
                if (zoomStartPoint) {
                    zoomEndPoint = {x: event.clientX, y: event.clientY};
                    // console.log(zoomStartPoint);
                    // console.log(zoomEndPoint);
                    // console.log("zoomStartPoint.x: " + zoomStartPoint.x + " || zoomStartPoint.y:" + zoomStartPoint.y + " || zoomEndPoint.x:" + event.clientX + " || zoomEndPoint.y:" + event.clientY + " || currentScale:" + currentScale);
                    //x,y永远是拉框的左上角坐标。
                    // var z_x = Math.min(zoomStartPoint.x, zoomEndPoint.x);
                    // var z_y = Math.min(zoomStartPoint.y, zoomEndPoint.y);

                    // 调整拉框范围方法(按上述计算左上角位置，只有从左上往下、右方向没问题，其他都不对（有放大倍数的情况下），所以作此调整),
                    // 先固定鼠标点击处为框体的左上角, 这样怎么拉都是向左和向下拉，然后计算好拉框的宽高, 以鼠标点击处为左上画好框后, 根据宽高处理计算向左或向上的框
                    var z_x = zoomStartPoint.x;
                    var z_y = zoomStartPoint.y;

                    // var width = (Math.abs(zoomStartPoint.x - zoomEndPoint.x)) / currentScale;
                    // var height = (Math.abs(zoomStartPoint.y - zoomEndPoint.y)) / currentScale;

                    var width = (Math.abs(zoomStartPoint.x - zoomEndPoint.x)) / currentScale;
                    var height = (Math.abs(zoomStartPoint.y - zoomEndPoint.y)) / currentScale;

                    // var selectionBoxX_svg_new = x - zoomDisX/currentScale;
                    // var selectionBoxY_svg_new = y - zoomDisY/currentScale;
                    // var selectionBoxX_svg_new = z_x - zoomDisX/currentScale;
                    // var selectionBoxY_svg_new = z_y - zoomDisY/currentScale;


                    var selectionBoxX_svg_new = z_x - zoomDisX/currentScale;
                    var selectionBoxY_svg_new = z_y - zoomDisY/currentScale;

                    if(event.clientX < zoomStartPoint.x){
                        selectionBoxX_svg_new = z_x - zoomDisX/currentScale - width;
                    }
                    if(event.clientY < zoomStartPoint.y){
                        selectionBoxY_svg_new = z_y - zoomDisY/currentScale - height;
                    }

                    // console.log("selectionBoxX_svg_new:"+ selectionBoxX_svg_new + " -- selectionBoxY_svg_new:" + selectionBoxY_svg_new);

                    selectionBox.attr({ x: selectionBoxX_svg_new, y: selectionBoxY_svg_new, width: width, height: height });
                }

            }
            else if(mouseDownType == "zoomMove"){
                // 框体的移动
                var dx=(event.clientX-deltax) / currentScale;
                var dy=(event.clientY-deltay) / currentScale;
                // console.log("dx:" + dx + " dy:" + dy + " || fx:" + fx + " || fy:" + fy);
                cobj.cy(fy+dy);
                cobj.cx(fx+dx);

                // console.log("rectSelG.length:" + rectSelG.length);
                if(rectSelG.length != 0) {
                    // console.log("拉框选中拖拽事件...处理选中图元...");

                    for(var n=0; n<rectSelG.length; n++){
                        //改变图元样式

                        var innerDia = rectSelG[n];
                        var pelId = innerDia.diaId;
                        var pelDoc = document.getElementById(pelId);

                        var pelDiaGeoPsrId = pelDoc.getAttribute("geoPsrId");

                        var gElement = $('#' + pelId);

                        // var transformAttr = gElement.attr('transform');
                        // console.log("transformAttr:" + transformAttr);

                        // var newTransform = tuneTransformAttr(transformAttr, dx, dy);
                        var newTransform = tuneTransformAttr(innerDia, dx, dy);

                        gElement.attr('transform', newTransform);




                        // ------------ 处理图元标签随图元拖动而联动 -----------
                        var diaLabelId = pelDiaGeoPsrId + "_label";
                        var diaLabelHtml = document.getElementById(diaLabelId);

                        // console.log("startPoint_relLabelMap ---------------------");
                        // console.log(startPoint_relLabelMap);
                        // console.log("diaLabelId ---------------------" + diaLabelId);
                        if (startPoint_relLabelMap.hasOwnProperty(diaLabelId)) {
                            var lb_x = startPoint_relLabelMap[diaLabelId].label_x;
                            var lb_y = startPoint_relLabelMap[diaLabelId].label_y;

                            if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                                // var label_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                                var label_newx = parseFloat(lb_x) + parseFloat(dx);
                                // var label_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                                var label_newy = parseFloat(lb_y) + parseFloat(dy);
                                diaLabelHtml.setAttribute("x", label_newx);
                                diaLabelHtml.setAttribute("y", label_newy);

                                var dhcl = diaLabelHtml.children.length;
                                if(dhcl == undefined || dhcl == null || typeof dhcl == "undefined"){
                                    dhcl = 0;
                                }

                                if (diaLabelHtml.children.length > 0) {
                                    // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                                    // 所有孩子节点，移动
                                    for (let i = 0; i < diaLabelHtml.children.length; i++) {
                                        const childNode = diaLabelHtml.children[i];
                                        if (childNode.nodeName.toLowerCase() === 'tspan') {
                                            // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                            // 更新x坐标
                                            childNode.setAttribute('x', label_newx);
                                            childNode.setAttribute('y', parseFloat(label_newy) + i*labelWrapHeight);
                                        } else {
                                            // 不是tspan节点，按需处理或忽略
                                        }
                                    }
                                } else {
                                    if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {

                                    }
                                }
                            }

                            let pExists = false;
                            // --- 处理图元量测标签随图元拖动而联动 ---
                            var diaMeasPId = pelDiaGeoPsrId + "_meas_p";
                            var diaMeasPHtml = document.getElementById(diaMeasPId);
                            if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                                pExists = true;
                                // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                                diaMeasPHtml.setAttribute("x", label_newx);
                                diaMeasPHtml.setAttribute("y", parseFloat(label_newy) + dhcl * labelWrapHeight);
                            }
                            var diaMeasQId = pelDiaGeoPsrId + "_meas_q";
                            var diaMeasQHtml = document.getElementById(diaMeasQId);
                            if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                                // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                                diaMeasQHtml.setAttribute("x", label_newx);
                                diaMeasQHtml.setAttribute("y", parseFloat(label_newy) + dhcl * labelWrapHeight + labelWrapHeight);
                            }

                            var diaVMPUId = pelDiaGeoPsrId + "_vm_pu";
                            var diaVMPUHtml = document.getElementById(diaVMPUId);
                            if(typeof diaVMPUHtml != "undefined" && diaVMPUHtml != null) {
                                // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                                diaVMPUHtml.setAttribute("x", label_newx);
                                let finalY;
                                if(pExists){
                                    finalY = parseFloat(label_newy) + dhcl * labelWrapHeight + labelWrapHeight * 2;
                                } else {
                                    finalY = parseFloat(label_newy) + dhcl * labelWrapHeight;
                                }
                                diaVMPUHtml.setAttribute("y", finalY);
                            }
                            var diaVAId = pelDiaGeoPsrId + "_v_a";
                            var diaVAHtml = document.getElementById(diaVAId);
                            if(typeof diaVAHtml != "undefined" && diaVAHtml != null) {
                                // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                                diaVAHtml.setAttribute("x", label_newx);
                                let finalY;
                                if(pExists){
                                    finalY = parseFloat(label_newy) + dhcl * labelWrapHeight + labelWrapHeight * 3;
                                } else {
                                    finalY = parseFloat(label_newy) + dhcl * labelWrapHeight + labelWrapHeight * 1;
                                }
                                diaVAHtml.setAttribute("y", finalY);
                            }


                        } else {
                            // console.log("diaLabelId: " + diaLabelId + " not found in startPoint_relLabelMap.");
                        }



                        var relVirtualLines = diaLineReMap[pelId];
                        // console.log("diaLineReMap ------------- ")
                        // console.log(diaLineReMap);
                        // console.log("----------- relVirtualLines ------------");
                        // console.log(pelId);
                        // console.log(relVirtualLines);

                        if(relVirtualLines != null && typeof relVirtualLines != undefined) {
                            // console.log(relVirtualLines.length);
                            for (var i = 0; i < relVirtualLines.length; i++) {
                                var virtualLineId = relVirtualLines[i];
                                // console.log("当前处理:" + virtualLineId);

                                var vlLine = virtualLinePointMap[virtualLineId];

                                var virLineStartGeoPsrId = vlLine.startPointPsrGeoId;
                                var virLineEndGeoPsrId = vlLine.endPointPsrGeoId;

                                // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                                var virtualLineHtml = document.getElementById(virtualLineId);
                                // console.log(virtualLineHtml);
                                let newX1, newY1;
                                if(typeof virtualLineHtml != "undefined" && virtualLineHtml != null) {
                                    if(virLineStartGeoPsrId == pelDiaGeoPsrId){    //开始节点连接
                                        // console.log("if in -----------------");
                                        const vlx = vlLine.startX;
                                        const vly = vlLine.startY;
                                        newX1 = (parseFloat(vlx) + parseFloat(dx));
                                        newY1 = (parseFloat(vly) + parseFloat(dy));

                                        virtualLineHtml.setAttribute("x1", newX1);
                                        virtualLineHtml.setAttribute("y1", newY1);
                                    } else if(virLineEndGeoPsrId == pelDiaGeoPsrId){
                                        // console.log("else in -----------------");
                                        const vlx = vlLine.endX;
                                        const vly = vlLine.endY;
                                        newX1 = (parseFloat(vlx) + parseFloat(dx));
                                        newY1 = (parseFloat(vly) + parseFloat(dy));

                                        virtualLineHtml.setAttribute("x2", newX1);
                                        virtualLineHtml.setAttribute("y2", newY1);

                                    } else{
                                        console.log("没找到");
                                    }

                                    // --------------------------- 处理线路标签 ---------------------------------
                                    var lineLabelId = virtualLineId + "_label";
                                    var lineLabelHtml = document.getElementById(lineLabelId);
                                    let x1_old, y1_old, x2_old, y2_old;
                                    if (virLineStartGeoPsrId == pelDiaGeoPsrId) {    // 开始节点连接
                                        x2_old = parseFloat(virtualLineHtml.getAttribute("x2"));
                                        y2_old = parseFloat(virtualLineHtml.getAttribute("y2"));
                                        x1_old = parseFloat(newX1);
                                        y1_old = parseFloat(newY1);
                                    } else if (virLineEndGeoPsrId == pelDiaGeoPsrId) {
                                        x1_old = parseFloat(virtualLineHtml.getAttribute("x1"));
                                        y1_old = parseFloat(virtualLineHtml.getAttribute("y1"));
                                        x2_old = parseFloat(newX1);
                                        y2_old = parseFloat(newY1);
                                    }

                                    // 计算线路角度
                                    // 计算方向向量
                                    let dx_lineLabel = x2_old - x1_old;
                                    let dy_lineLabel = y2_old - y1_old;
                                    // 计算标签旋转角度（弧度），并转换为角度。
                                    let angle_label = Math.atan2(dy_lineLabel, dx_lineLabel) * (180 / Math.PI);

                                    // 计算标签的中心点
                                    let centerX = (x1_old + x2_old) / 2;
                                    let centerY = (y1_old + y2_old) / 2;

                                    // 计算标签到中心点的距离
                                    let distanceFromCenter = 15; // 固定距离，可以根据需要调整

                                    // 计算标签的新位置
                                    let offsetX = distanceFromCenter * Math.cos(angle_label * Math.PI / 180 + Math.PI / 2);
                                    let offsetY = distanceFromCenter * Math.sin(angle_label * Math.PI / 180 + Math.PI / 2);

                                    let newX = centerX + offsetX;
                                    let newY = centerY + offsetY;

                                    // 设置标签元素的位置和旋转属性
                                    lineLabelHtml.setAttribute("x", newX);
                                    lineLabelHtml.setAttribute("y", newY);
                                    lineLabelHtml.setAttribute("transform", `rotate(${angle_label}, ${newX}, ${newY})`);

                                } else {
                                    // 考虑是不是有拐点的线段
                                    var relVirtualLineId_0 = virtualLineId + ",0";
                                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                    if(relVirtualLine_0_HTML!= null){
                                        var virtualPointStr = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                        var vpSize = virtualPointStr.split(";").length;
                                        var relVirtualLine_end_HTML = document.getElementById(virtualLineId + "," + vpSize);
                                        // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                                        /*var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                        var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                                        console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                        virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                        virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                        console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);*/

                                        if(virLineStartGeoPsrId == pelDiaGeoPsrId){    //开始节点连接
                                            // console.log("连接在带拐点的线的开始段");
                                            const vlx = vlLine.startX;
                                            const vly = vlLine.startY;
                                            const newX1 = (parseFloat(vlx) + parseFloat(dx));
                                            const newY1 = (parseFloat(vly) + parseFloat(dy));
                                            relVirtualLine_0_HTML.setAttribute("x1", newX1);
                                            relVirtualLine_0_HTML.setAttribute("y1", newY1);


                                            // 处理线路标签
                                            var diaLabelId = virtualLineId + "_label";
                                            var lineLabelHtml = document.getElementById(diaLabelId);
                                            let x1_old, y1_old, x2_old, y2_old;

                                            x2_old = parseFloat(relVirtualLine_0_HTML.getAttribute("x2"));
                                            y2_old = parseFloat(relVirtualLine_0_HTML.getAttribute("y2"));
                                            x1_old = parseFloat(newX1);
                                            y1_old = parseFloat(newY1);

                                            // 计算线路角度
                                            // 计算方向向量
                                            let dx_lineLabel = x2_old - x1_old;
                                            let dy_lineLabel = y2_old - y1_old;
                                            // 计算标签旋转角度（弧度），并转换为角度。
                                            let angle_label = Math.atan2(dy_lineLabel, dx_lineLabel) * (180 / Math.PI);

                                            // 计算标签的中心点
                                            let centerX = (x1_old + x2_old) / 2;
                                            let centerY = (y1_old + y2_old) / 2;

                                            // 计算标签到中心点的距离
                                            let distanceFromCenter = 15; // 固定距离，可以根据需要调整

                                            // 计算标签的新位置
                                            let offsetX = distanceFromCenter * Math.cos(angle_label * Math.PI / 180 + Math.PI / 2);
                                            let offsetY = distanceFromCenter * Math.sin(angle_label * Math.PI / 180 + Math.PI / 2);

                                            let newX = centerX + offsetX;
                                            let newY = centerY + offsetY;

                                            // 设置标签元素的位置和旋转属性
                                            lineLabelHtml.setAttribute("x", newX);
                                            lineLabelHtml.setAttribute("y", newY);
                                            lineLabelHtml.setAttribute("transform", `rotate(${angle_label}, ${newX}, ${newY})`);
                                        } else if(virLineEndGeoPsrId == pelDiaGeoPsrId){
                                            // console.log("连接在带拐点的线的结束段");
                                            const vlx = vlLine.endX;
                                            const vly = vlLine.endY;
                                            const newX1 = (parseFloat(vlx) + parseFloat(dx));
                                            const newY1 = (parseFloat(vly) + parseFloat(dy));
                                            relVirtualLine_end_HTML.setAttribute("x2", newX1);
                                            relVirtualLine_end_HTML.setAttribute("y2", newY1);
                                        }
                                    }
                                }

                            }
                        }

                    }
                }

                if(rectSelG_Line.length != 0) {
                    // console.log("拉框选中拖拽事件...处理选中的母线...");

                    for(var n=0; n<rectSelG_Line.length; n++){
                        // 改变图元样式

                        var innerDia_line = rectSelG_Line[n];
                        var pelId = innerDia_line.diaId;
                        var pelDoc = document.getElementById(pelId);

                        var pelDiaGeoPsrId = pelDoc.getAttribute("geoPsrId");

                        var gElement = $('#' + pelId);

                        const startX1 = innerDia_line.startX1;
                        const startY1 = innerDia_line.startY1;
                        const startX2 = innerDia_line.startX2;
                        const startY2 = innerDia_line.startY2;


                        const startX1_new = parseFloat(startX1) + parseFloat(dx);
                        const startY1_new = parseFloat(startY1) + parseFloat(dy);
                        const startX2_new = parseFloat(startX2) + parseFloat(dx);
                        const startY2_new = parseFloat(startY2) + parseFloat(dy);

                        gElement.attr('x1', startX1_new);
                        gElement.attr('y1', startY1_new);
                        gElement.attr('x2', startX2_new);
                        gElement.attr('y2', startY2_new);


                        // ----- 处理图元标签随图元拖动而联动 -----
                        var diaLabelId = pelDiaGeoPsrId + "_label";
                        var diaLabelHtml = document.getElementById(diaLabelId);

                        if (startPoint_relLabelMap.hasOwnProperty(diaLabelId)) {
                            var lb_x = startPoint_relLabelMap[diaLabelId].label_x;
                            var lb_y = startPoint_relLabelMap[diaLabelId].label_y;

                            if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                                // var label_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                                var label_newx = parseFloat(lb_x) + parseFloat(dx);
                                // var label_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                                var label_newy = parseFloat(lb_y) + parseFloat(dy);
                                diaLabelHtml.setAttribute("x", label_newx);
                                diaLabelHtml.setAttribute("y", label_newy);

                                var dhcl = diaLabelHtml.children.length;
                                if(dhcl == undefined || dhcl == null || typeof dhcl == "undefined"){
                                    dhcl = 0;
                                }

                                if (diaLabelHtml.children.length > 0) {
                                    // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                                    // 所有孩子节点，移动
                                    for (let i = 0; i < diaLabelHtml.children.length; i++) {
                                        const childNode = diaLabelHtml.children[i];
                                        if (childNode.nodeName.toLowerCase() === 'tspan') {
                                            // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                            // 更新x坐标
                                            childNode.setAttribute('x', label_newx);
                                            childNode.setAttribute('y', parseFloat(label_newy) + i*labelWrapHeight);
                                        } else {
                                            // 不是tspan节点，按需处理或忽略
                                        }
                                    }
                                } else {
                                    if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {

                                    }
                                }
                            }

                            // --- 处理图元量测标签随图元拖动而联动 ---
                            var diaMeasPId = pelDiaGeoPsrId + "_meas_p";
                            var diaMeasPHtml = document.getElementById(diaMeasPId);
                            if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                                // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                                diaMeasPHtml.setAttribute("x", label_newx);
                                diaMeasPHtml.setAttribute("y", parseFloat(label_newy) + dhcl * labelWrapHeight);
                            }
                            var diaMeasQId = pelDiaGeoPsrId + "_meas_q";
                            var diaMeasQHtml = document.getElementById(diaMeasQId);
                            if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                                // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                                diaMeasQHtml.setAttribute("x", label_newx);
                                diaMeasQHtml.setAttribute("y", parseFloat(label_newy) + dhcl * labelWrapHeight + labelWrapHeight);
                            }


                        } else {
                            // console.log("diaLabelId: " + diaLabelId + " not found in startPoint_relLabelMap.");
                        }













                        // console.log("tarId:" + pelId);
                        // console.log(diaLineReMap);
                        var relVirtualLines = diaLineReMap[pelId];
                        // console.log("mousemove -- relVirtualLines: " + relVirtualLines);

                        if(relVirtualLines != null && typeof relVirtualLines != undefined) {

                            for (var i = 0; i < relVirtualLines.length; i++) {
                                var virtualLineId = relVirtualLines[i];
                                // console.log("当前处理:" + virtualLineId);

                                var virtualLineHtml = document.getElementById(virtualLineId);
                                // console.log(virtualLineHtml);
                                if(typeof virtualLineHtml != "undefined" && virtualLineHtml != null) {
                                    // 没有拐点
                                    var vlLine = virtualLinePointMap[virtualLineId];

                                    var virLineStartGeoPsrId = vlLine.startPointPsrGeoId;
                                    var virLineEndGeoPsrId = vlLine.endPointPsrGeoId;

                                    virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                    virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];

                                    var vlx = vlLine.startX;
                                    var vly = vlLine.startY;
                                    // console.log("vlx:" + vlx + " || vly:" + vly);
                                    var newX1 = (parseFloat(vlx) + parseFloat(dx));
                                    var newY1 = (parseFloat(vly) + parseFloat(dy));

                                    if(virLineStartGeoPsrId == pelDiaGeoPsrId){    //开始节点连接
                                        // console.log("if in -----------------");
                                        virtualLineHtml.setAttribute("x1", newX1);
                                        virtualLineHtml.setAttribute("y1", newY1);
                                    } else if(virLineEndGeoPsrId == pelDiaGeoPsrId){
                                        // console.log("else in -----------------");
                                        vlx = vlLine.endX;
                                        vly = vlLine.endY;
                                        newX1 = (parseFloat(vlx) + parseFloat(dx));
                                        newY1 = (parseFloat(vly) + parseFloat(dy));

                                        virtualLineHtml.setAttribute("x2", newX1);
                                        virtualLineHtml.setAttribute("y2", newY1);
                                    }
                                } else {
                                    // 考虑是不是有拐点的线段
                                    var relVirtualLineId_0 = virtualLineId + ",0";
                                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                    if (relVirtualLine_0_HTML != null) {
                                        var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                        var vpSize = virtualPoint.split(";").length;
                                        var relVirtualLine_end_HTML = document.getElementById(virtualLineId + "," + vpSize);
                                        // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2

                                        // console.log(virtualLineId);
                                        var vlLine = virtualLinePointMap[virtualLineId];
                                        // console.log(vlLine);

                                        var virLineStartGeoPsrId = vlLine.startPointPsrGeoId;
                                        var virLineEndGeoPsrId = vlLine.endPointPsrGeoId;

                                        virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                        virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];

                                        var vlx = vlLine.startX;
                                        var vly = vlLine.startY;
                                        var newX1 = (parseFloat(vlx) + parseFloat(dx));
                                        var newY1 = (parseFloat(vly) + parseFloat(dy));

                                        if(virLineStartGeoPsrId == pelDiaGeoPsrId){    //开始节点连接
                                            // console.log("if in -----------------");
                                            relVirtualLine_0_HTML.setAttribute("x1", newX1);
                                            relVirtualLine_0_HTML.setAttribute("y1", newY1);
                                        } else if(virLineEndGeoPsrId == pelDiaGeoPsrId){
                                            // console.log("else in -----------------");
                                            vlx = vlLine.endX;
                                            vly = vlLine.endY;
                                            newX1 = (parseFloat(vlx) + parseFloat(dx));
                                            newY1 = (parseFloat(vly) + parseFloat(dy));

                                            relVirtualLine_end_HTML.setAttribute("x2", newX1);
                                            relVirtualLine_end_HTML.setAttribute("y2", newY1);
                                        }
                                    }
                                }


                                // ----------- 当前情况下，拉框选中的线路只有母线，所以需要处理母线上对的点  ----------------
                                //知道拖动母线时带动的虚拟连接线的ID
                                //通过虚拟连接线ID,找到与母线连接的点,将点的圆心也设置为newX1和newY1
                                // console.log("virtualLineId:" + virtualLineId);
                                var mpId = virtualLineId + "&" + pelDiaGeoPsrId;
                                var busLineConnPoint = virLine2BusPointMap[mpId];
                                // console.log("busLineConnPoint --------------");
                                // console.log(busLineConnPoint);
                                var busLineConnPointId = busLineConnPoint.id;
                                var busLineConnPointHtml = document.getElementById(busLineConnPointId);
                                busLineConnPointHtml.setAttribute("cx", newX1);
                                busLineConnPointHtml.setAttribute("cy", newY1);


                            }

                        }
                    }
                }

                if(rectSelG_VirtualPoint != 0) {
                    for(var n=0; n<rectSelG_VirtualPoint.length; n++){
                        var innerDia = rectSelG_VirtualPoint[n];
                        var pelId = innerDia.diaId;


                        var pelDoc = document.getElementById(pelId);

                        var startX = innerDia.startX;
                        var startY = innerDia.startY;

                        var x_new = parseFloat(startX) + parseFloat(dx);
                        var y_new = parseFloat(startY) + parseFloat(dy);

                        pelDoc.setAttribute("cx", x_new);
                        pelDoc.setAttribute("cy", y_new);


                        // 处理相连的线段
                        // ngpg13511134@@25bc4f95-5609-4491-8ea2-b03500ffb50b_node,1
                        var szlineId = pelId.substring(0, pelId.indexOf("_node"));
                        var nodeNum = pelId.split(",")[1];

                        var szLineHTML = document.getElementById(szlineId + ",0");
                        // 处理拐点带动的两侧线段
                        var nStart = pelDoc.getAttribute('start');
                        var nEnd = pelDoc.getAttribute('end');

                        var nStartLineHtml = document.getElementById(nStart);
                        if(nStartLineHtml != null) {
                            var nStartLineStartGeoPsrId = nStartLineHtml.getAttribute("startGeoPsrId");
                            var nStartLineEndGeoPsrId = nStartLineHtml.getAttribute("endGeoPsrId");
                            if(nStartLineStartGeoPsrId == pelId){
                                nStartLineHtml.setAttribute("x1", x_new);
                                nStartLineHtml.setAttribute("y1", y_new);
                            } else if(nStartLineEndGeoPsrId == pelId){
                                nStartLineHtml.setAttribute("x2", x_new);
                                nStartLineHtml.setAttribute("y2", y_new);
                            }
                        }

                        var nEndLineHtml = document.getElementById(nEnd);
                        if(nEndLineHtml != null) {
                            var nEndLineStartGeoPsrId = nEndLineHtml.getAttribute("startGeoPsrId");
                            var nEndLineEndGeoPsrId = nEndLineHtml.getAttribute("endGeoPsrId");
                            if(nEndLineStartGeoPsrId == pelId){
                                nEndLineHtml.setAttribute("x1", x_new);
                                nEndLineHtml.setAttribute("y1", y_new);
                            }else if(nEndLineEndGeoPsrId == pelId){
                                nEndLineHtml.setAttribute("x2", x_new);
                                nEndLineHtml.setAttribute("y2", y_new);
                            }
                        }

                        // 把主线段（0段）的属性改下
                        var lineVirtualPoint = szLineHTML.getAttribute("virtualPoint");
                        var lineVirtualPointArray = lineVirtualPoint.split(";");
                        var lineVirtualPointArray_new = "";
                        for(var iii=0; iii<lineVirtualPointArray.length; iii++) {
                            if(iii == nodeNum){
                                lineVirtualPointArray_new = lineVirtualPointArray_new + x_new + "," + y_new + ";";
                            } else {
                                lineVirtualPointArray_new = lineVirtualPointArray_new + lineVirtualPointArray[iii] + ";";
                            }
                        }
                        if(lineVirtualPointArray_new.endsWith(";")){
                            lineVirtualPointArray_new = lineVirtualPointArray_new.substring(0, lineVirtualPointArray_new.length-1);
                        }
                        szLineHTML.setAttribute("virtualPoint", lineVirtualPointArray_new);

                    }


                }

            }
            else if(mouseDownType == "LINEFRAME"){
                console.log("线框拖动");

                // 计算新的坐标
                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;

                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                var diaGeoPsrId = tarDHtml.getAttribute("geoPsrId");

                // 1、调整path
                // M10440.451416015625 2355.42529296875 L10680.451416015625 2355.42529296875 L10680.451416015625 2480.67529296875 L10440.451416015625 2480.67529296875 L10440.451416015625 2355.42529296875 Z
                var startPathD = startPoint.startPathD;
                console.log("startPathD:" + startPathD);
                if(startPathD != null && startPathD != ""){
                    let pathDL;
                    if(startPathD.endsWith("Z")){
                        pathDL = startPathD.substring(1, startPathD.length - 1);  //去除开头的M和结尾的Z
                    } else {
                        pathDL = startPathD.substring(1, startPathD.length);  //去除开头的M和结尾的Z
                    }
                    console.log("pathDL:" + pathDL);
                    var pathDLs = pathDL.split("L");  //按L分割分成多个点
                    var newPathDLs = "";
                    for(var i = 0; i < pathDLs.length; i++){
                        var pathDLs_i = pathDLs[i];
                        var pathDLs_i_arr = pathDLs_i.split(" ");
                        var x = parseFloat(pathDLs_i_arr[0]) + parseFloat(dx);
                        var y = parseFloat(pathDLs_i_arr[1]) + parseFloat(dy);
                        if(i == 0){
                            newPathDLs = x + " " + y;
                        }else{
                            newPathDLs = newPathDLs + " L" + x + " " + y;
                        }
                    }
                    var newPathD = "M" + newPathDLs + " Z";
                    console.log("newPathD:" + newPathD);
                    tarDHtml.setAttribute("d", newPathD);
                }

                // --- 处理图元标签随图元拖动而联动 ---
                var diaLabelId = diaGeoPsrId + "_label";
                var diaLabelHtml = document.getElementById(diaLabelId);

                var label_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                var label_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                    var dhcl = 0;
                    if(diaLabelHtml.children == null || typeof(diaLabelHtml.children) == "undefined"){
                        dhcl = 0;
                    } else {
                        dhcl = diaLabelHtml.children.length;
                    }

                    /*var dhcl = diaLabelHtml.children.length;
                    if(dhcl == undefined || dhcl == null || typeof dhcl == "undefined"){
                        dhcl = 0;
                    }*/

                    if (diaLabelHtml.children != null && typeof(diaLabelHtml.children) != "undefined" && diaLabelHtml.children.length > 0) {
                        // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                        // 所有孩子节点，移动
                        for (let i = 0; i < diaLabelHtml.children.length; i++) {
                            const childNode = diaLabelHtml.children[i];
                            if (childNode.nodeName.toLowerCase() === 'tspan') {
                                // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                // 更新x坐标
                                childNode.setAttribute('x', label_newx);
                                // childNode.setAttribute('y', parseFloat(label_newy) - (diaLabelHtml.children.length - i) * 10 + 20);
                                childNode.setAttribute('y', parseFloat(label_newy) + i*labelWrapHeight);
                                // childNode.setAttribute("y", parseFloat(label_newy) + i * 10);
                            } else {
                                // 不是tspan节点，按需处理或忽略
                            }
                        }
                        diaLabelHtml.setAttribute("x", label_newx);
                        diaLabelHtml.setAttribute("y", label_newy);
                    } else {
                        if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                            // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                            diaLabelHtml.setAttribute("x", label_newx);
                            diaLabelHtml.setAttribute("y", label_newy);
                        }
                    }
                }
            }
            else if(mouseDownType == "virtualPoint"){
                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                // 计算新的坐标
                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;

                const newX = fx + eval(dx);
                const newY = fy + eval(dy);

                cobj.cx(newX);
                cobj.cy(newY);

                // 处理拐点带动的两侧线段
                var nStart = cobj.attr('start');
                var nEnd = cobj.attr('end');

                var nStartLineHtml = document.getElementById(nStart);
                if(nStartLineHtml != null) {
                    var nStartLineStartGeoPsrId = nStartLineHtml.getAttribute("startGeoPsrId");
                    var nStartLineEndGeoPsrId = nStartLineHtml.getAttribute("endGeoPsrId");
                    if(nStartLineStartGeoPsrId == tarId){
                        nStartLineHtml.setAttribute("x1", newX);
                        nStartLineHtml.setAttribute("y1", newY);
                    } else if(nStartLineEndGeoPsrId == tarId){
                        nStartLineHtml.setAttribute("x2", newX);
                        nStartLineHtml.setAttribute("y2", newY);
                    }
                }

                var nEndLineHtml = document.getElementById(nEnd);
                if(nEndLineHtml != null) {
                    var nEndLineStartGeoPsrId = nEndLineHtml.getAttribute("startGeoPsrId");
                    var nEndLineEndGeoPsrId = nEndLineHtml.getAttribute("endGeoPsrId");
                    if(nEndLineStartGeoPsrId == tarId){
                        nEndLineHtml.setAttribute("x1", newX);
                        nEndLineHtml.setAttribute("y1", newY);
                    }else if(nEndLineEndGeoPsrId == tarId){
                        nEndLineHtml.setAttribute("x2", newX);
                        nEndLineHtml.setAttribute("y2", newY);
                    }
                }

                // 如果是第一个拐点，则需要处理线路标签
                let nodeNum = tarId.split(",")[1];
                if(nodeNum == 0){
                    let szLineId = tarId.substring(0, tarId.indexOf("_node"));  // 拐点所在线路
                    var relVirtualLineId_0 = szLineId + ",0";
                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                    if(relVirtualLine_0_HTML!= null){
                        // 处理线路标签
                        let x1 = relVirtualLine_0_HTML.getAttribute("x1");
                        let y1 = relVirtualLine_0_HTML.getAttribute("y1");

                        var diaLabelId = szLineId + "_label";
                        var lineLabelHtml = document.getElementById(diaLabelId);

                        // 计算线路角度
                        // 计算方向向量
                        let dx_lineLabel = parseFloat(newX) - parseFloat(x1);
                        let dy_lineLabel = parseFloat(newY) - parseFloat(y1);
                        // 计算标签旋转角度（弧度），并转换为角度。
                        let angle_label = Math.atan2(dy_lineLabel, dx_lineLabel) * (180 / Math.PI);

                        // 计算标签的中心点
                        let centerX = (parseFloat(x1) + parseFloat(newX)) / 2;
                        let centerY = (parseFloat(y1) + parseFloat(newY)) / 2;

                        // 计算标签到中心点的距离
                        let distanceFromCenter = 15; // 固定距离，可以根据需要调整

                        // 计算标签的新位置
                        let offsetX = distanceFromCenter * Math.cos(angle_label * Math.PI / 180 + Math.PI / 2);
                        let offsetY = distanceFromCenter * Math.sin(angle_label * Math.PI / 180 + Math.PI / 2);

                        centerX = centerX + offsetX;
                        centerY = centerY + offsetY;

                        // 设置标签元素的位置和旋转属性
                        lineLabelHtml.setAttribute("x", centerX);
                        lineLabelHtml.setAttribute("y", centerY);
                        lineLabelHtml.setAttribute("transform", `rotate(${angle_label}, ${centerX}, ${centerY})`);

                    }

                }


            }
            else if(mouseDownType == "busPoint"){
                // 拖动母线连接点，目前母线仅考虑水平方向，所以拖动时y是固定的，x在母线的宽度范围内
                // 后面要考虑计算母线斜率，拖动时沿着母线拖动
                var tarId = cobj.attr('id');
                if(tarId == "8824218e-08c3-497a-9e78-b85bb3612a55" || tarId == "09908b4c-4dbb-4ea0-833c-c5e1423c01db"){
                    console.log("debug");
                }
                var tarDHtml = document.getElementById(tarId);

                // 计算新的坐标
                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;
                dy = 0; // 暂时只考虑水平方向的母线

                var newX = fx + eval(dx);
                var newY = fy + eval(dy);

                // 判断母线长度范围，拉动时x范围不超过母线长度范围
                var busPointHTML = document.getElementById(tarId);
                var geoPsrIdBusLine = busPointHTML.getAttribute("geoPsrIdBusLine");
                var busDia = geoPsrId2DiaMap[geoPsrIdBusLine];
                var busDiaId = busDia.id;
                var busLineHTML = document.getElementById(busDiaId);
                var bus_x1 = busLineHTML.getAttribute("x1");
                var bus_y1 = busLineHTML.getAttribute("y1");
                var bus_x2 = busLineHTML.getAttribute("x2");
                var bus_y2 = busLineHTML.getAttribute("y2");

                if(newX > Math.max(eval(bus_x1), eval(bus_x2))){
                    newX = Math.max(eval(bus_x1), eval(bus_x2));
                }
                if(newX < Math.min(eval(bus_x1), eval(bus_x2))){
                    newX = Math.min(eval(bus_x1), eval(bus_x2));
                }

                cobj.cx(newX);
                cobj.cy(newY);

                // 处理连接线
                var virLineId = busPointHTML.getAttribute("virLineId");
                var virLineHTML = document.getElementById(virLineId);

                if(typeof virLineHTML != "undefined" && virLineHTML != null) {
                    // 没有拐点
                    var virLineStartGeoPsrId = virLineHTML.getAttribute("startGeoPsrId");
                    var virLineEndGeoPsrId = virLineHTML.getAttribute("endGeoPsrId");

                    virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                    virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];

                    var geoPsrIdDia = busPointHTML.getAttribute("geoPsrIdDia");
                    geoPsrIdDia = merToMainMap[geoPsrIdDia];    //取其合并主节点ID

                    if(virLineStartGeoPsrId == geoPsrIdDia){
                        if(virLineStartGeoPsrId == geoPsrIdBusLine){
                            // 母线连接点的bus和dia一样
                            // 与母线连接的虚拟连接线结束端是 非母线连接点, 则母线端在开始端
                            virLineHTML.setAttribute("x1", newX);
                        } else {
                            // 与母线连接的虚拟连接线开始端是 非母线连接点, 则母线端在结束端
                            virLineHTML.setAttribute("x2", newX);
                        }
                    } else if(virLineEndGeoPsrId == geoPsrIdDia){
                        if(virLineEndGeoPsrId == geoPsrIdBusLine){
                            // 母线连接点的bus和dia一样
                            // 与母线连接的虚拟连接线结束端是 非母线连接点, 则母线端在开始端
                            virLineHTML.setAttribute("x2", newX);
                        } else {
                            // 与母线连接的虚拟连接线结束端是 非母线连接点, 则母线端在开始端
                            virLineHTML.setAttribute("x1", newX);
                        }
                    }
                } else {
                    // 考虑是不是有拐点的线段
                    var relVirtualLineId_0 = virLineId + ",0";
                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                    if (relVirtualLine_0_HTML != null) {
                        var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                        var vpSize = virtualPoint.split(";").length;
                        var relVirtualLine_end_HTML = document.getElementById(virLineId + "," + vpSize);
                        // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                        var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                        var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                        virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                        virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];

                        var geoPsrIdDia = busPointHTML.getAttribute("geoPsrIdDia");
                        geoPsrIdDia = merToMainMap[geoPsrIdDia];    //取其合并主节点ID

                        if(virLineStartGeoPsrId == geoPsrIdDia){
                            // 与母线连接的虚拟连接线开始端是 非母线连接点, 则母线端在结束端
                            relVirtualLine_end_HTML.setAttribute("x2", newX);
                        } else if(virLineEndGeoPsrId == geoPsrIdDia){
                            // 与母线连接的虚拟连接线结束端是 非母线连接点, 则母线端在开始端
                            relVirtualLine_0_HTML.setAttribute("x1", newX);
                        }
                    }

                }
            }
            else {

            }
        }
    });

    $("#drawPdSvg").bind("mouseup", function(event){
        if (changeModel) {
            // console.log("mouseup ---- mouseDownType + " + mouseDownType + " ---- (" + event.clientX + "," + event.clientY + ")");

            var targetElement=event.target;
            // console.log(targetElement);

            var tarName = targetElement.tagName;
            // console.log(tarName);

            var targetElementSVG = SVG.adopt(targetElement);

            //鼠标抬起时，处理鼠标移动后的坐标更新事件
            if(mouseDownType == "transformer2td" || mouseDownType == "trans0303" || mouseDownType == "cableTerminal" || mouseDownType == "disconnector_1" || mouseDownType == "tower"
                || mouseDownType == "dropOutFuse" || mouseDownType == "transformer0110" || mouseDownType == "zsBreaker" || mouseDownType == "handSwitch"
                || mouseDownType == "outLinePoint" || mouseDownType == "fac" || mouseDownType == "mediumVPoint" || mouseDownType == "LS"
                || mouseDownType == "fuseDisc_1" || mouseDownType == "discGround_0" || mouseDownType == "transformer0313" || mouseDownType == "transformer0314"
                || mouseDownType == "arresterGround" || mouseDownType == "zsDisc" || mouseDownType == "lineFaultIndicator" || mouseDownType == "sureA"
                || mouseDownType == "zsLS" || mouseDownType == "cableIntermediateJoint" || mouseDownType == "lsoDisc" || mouseDownType == "disc_hand_0306"
                || mouseDownType == "discHand_fuse_0306" || mouseDownType == "transSingle0313" || mouseDownType == "trans_3_2_0313"
                || mouseDownType == "trans_vol_4_0314"){
                // console.log("mouseup -- mouseDownType： " + mouseDownType + "-------------------------------- mouseup");

                var tarId = cobj.attr('id');
                // console.log("tarId=" + tarId);
                // console.log(cobj);
                // console.log("selectedPelId:" + selectedPelId);

                var tarDHtml = document.getElementById(tarId);
                // console.log(tarDHtml);

                var directionMap = {
                    0: { x: dx, y: dy },
                    90: { x: dy, y: 0 - parseFloat(dx)},
                    180: { x: 0 - parseFloat(dx), y: 0 - parseFloat(dy)},
                    270: { x: 0 - parseFloat(dy), y: dx }
                };

                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;
                // console.log("本次移动变动的距离为：dx=" + dx + ", dy=" + dy);

                // 获取当前旋转角度对应的映射
                var rotateAngle = 0;
                var transform_a = cobj.attr("transform");
                // console.log("transform_a:" + transform_a);
                if(typeof transform_a == "undefined" || transform_a == "undefined" || transform_a.indexOf('rotate') == -1){

                } else {
                    rotateAngle = cobj.attr("transform").split("rotate(")[1].split(")")[0];
                }
                rotateAngle = dealRotate(rotateAngle);
                var direction = directionMap[parseInt(rotateAngle)];

                // 计算新的坐标

                const newX = parseFloat(fx) + parseFloat(direction.x);
                const newY = parseFloat(fy) + parseFloat(direction.y);

                var useType = targetElementSVG.attr('xlink:href');
                // console.log("useType: " + useType);

                // 原本在此处还添加了一层判断，判断useType 是否为这些可以拖动的图元，但偶尔会获取到null（实际不应该为null）导致单次拖动后坐标没更新，所以去除了
                var diaTransform = tarDHtml.getAttribute("transform");
                var xstart = diaTransform.indexOf("(");
                var xend = diaTransform.indexOf(" ");
                var yend = diaTransform.indexOf(")");
                var rotatestart = diaTransform.indexOf("rotate");
                var geoPsrId = tarDHtml.getAttribute("geoPsrId");
                // console.log("xstart:" + xstart + " | xend:" + xend + " | yend:" + yend + " | rotatestart:" + rotatestart);

                var x_old = diaTransform.substring(xstart+1, xend);
                var y_old = diaTransform.substring(xend+1, yend);

                var rotateS = diaTransform.substring(rotatestart, diaTransform.length);

                var x_new = parseFloat(x_old) + parseFloat(dx);
                var y_new = parseFloat(y_old) + parseFloat(dy);

                // console.log("x_old:" + x_old + " | y_old:" + y_old);
                // console.log("x_new:" + x_new + " | y_new:" + y_new);

                var transform = "translate(" + x_new + " " + y_new + ") " + rotateS;
                tarDHtml.setAttribute("transform", transform);
                // transform="translate(8805.1953125 3050.20654296875) rotate(270)"
                tarDHtml.removeAttribute("x");
                tarDHtml.removeAttribute("y");


                var label_newx = "";
                var label_newy = "";
                // ------------------- 处理图元标签随图元拖动而联动 -----------------
                var diaLabelId = geoPsrId + "_label";
                var diaLabelHtml = document.getElementById(diaLabelId);
                // console.log("diaLabelHtml --------------------- ");
                // console.log(diaLabelHtml);
                if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                    // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                    label_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                    label_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                    // diaLabelHtml.setAttribute("x", label_newx);
                    // diaLabelHtml.setAttribute("y", label_newy);
                }

                var coordinates = tarDHtml.getAttribute("coordinates");
                // console.log("coordinates:" + coordinates);

                var coordinateString = coordinates.substring(coordinates.indexOf("(") + 1, coordinates.indexOf(")"));

                var coordX = coordinateString.split(' ')[0];
                var coordY = coordinateString.split(' ')[1];

                /*var coordX = matches[0];
                var coordY = matches[1];*/

                var coordX_new = parseFloat(coordX) + parseFloat(dx);
                var coordY_new = parseFloat(coordY) + parseFloat(dy);

                var coordinates_new = "POINT(" + coordX_new + " " + coordY_new + ")";
                console.log("coordinates_new:" + coordinates_new);
                tarDHtml.setAttribute("coordinates", coordinates_new);
                updateCoordById(tarId, coordinates_new, null, label_newx, label_newy);

                // 全局变量需要更新
                geoPsrId2DiaMap[geoPsrId].coordinates = coordinates_new;

                // ---------------------- 调整站房坐标 -------------------------
                updateZfAutoSize(tarId);



                // 动态变更图元间虚拟连接线
                var relVirtualLines = diaLineReMap[tarId];
                if(relVirtualLines != null && typeof relVirtualLines != undefined) {
                    for (var i = 0; i < relVirtualLines.length; i++) {
                        var virtualLineId = relVirtualLines[i];
                        // console.log("当前处理:" + virtualLineId);
                    }
                }

            }
            else if(mouseDownType == "母线"){
                // console.log("母线抬起");
                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                var geoPsrId_busLine = tarDHtml.getAttribute("geoPsrId");

                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;

                var x1_new = parseFloat(startPoint.lineStartX1) + parseFloat(dx);
                var y1_new = parseFloat(startPoint.lineStartY1) + parseFloat(dy);
                var x2_new = parseFloat(startPoint.lineStartX2) + parseFloat(dx);
                var y2_new = parseFloat(startPoint.lineStartY2) + parseFloat(dy);

                tarDHtml.setAttribute("x1", x1_new);
                tarDHtml.setAttribute("y1", y1_new);
                tarDHtml.setAttribute("x2", x2_new);
                tarDHtml.setAttribute("y2", y2_new);

                var coordinates_new = "LINESTRING(" + x1_new + " " + y1_new + "," + x2_new + " " + y2_new + ")";
                tarDHtml.setAttribute("coordinates", coordinates_new);

                var label_newx = "";
                var label_newy = "";
                // ------------------- 处理图元标签随图元拖动而联动 -----------------
                var diaLabelId = geoPsrId_busLine + "_label";
                var diaLabelHtml = document.getElementById(diaLabelId);
                if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                    // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                    label_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                    label_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                }

                // 全局变量需要更新
                geoPsrId2DiaMap[geoPsrId_busLine].coordinates = coordinates_new;

                updateBusLineByIdAndDxDy(tarId, geoPsrId_busLine, coordinates_new, dx, dy, label_newx, label_newy);

                // ---------------------- 调整站房坐标 -------------------------
                updateZfAutoSize(tarId);
            }
            else if(mouseDownType == "连接线"){
                // console.log("连接线抬起");
                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;

                var x1_new = parseFloat(startPoint.lineStartX1) + parseFloat(dx);
                var y1_new = parseFloat(startPoint.lineStartY1) + parseFloat(dy);
                var x2_new = parseFloat(startPoint.lineStartX2) + parseFloat(dx);
                var y2_new = parseFloat(startPoint.lineStartY2) + parseFloat(dy);

                tarDHtml.setAttribute("x1", x1_new);
                tarDHtml.setAttribute("y1", y1_new);
                tarDHtml.setAttribute("x2", x2_new);
                tarDHtml.setAttribute("y2", y2_new);

                var coordinates_new = "LINESTRING(" + x1_new + " " + y1_new + "," + x2_new + " " + y2_new + ")";
                tarDHtml.setAttribute("coordinates", coordinates_new);

                updateCoordById(tarId, coordinates_new, null, null, null);
            }
            else if(mouseDownType == "lineRePoint"){
                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);
                var coordinates = tarDHtml.getAttribute("coordinates");

                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;

                var coordinateString = coordinates.substring(coordinates.indexOf("(") + 1, coordinates.indexOf(")"));
                // 分割坐标字符串为两组（或多组）x和y
                var coordinatesArray = coordinateString.split(",");
                var coordinates_new = "LINESTRING(";
                for(var i=0; i<coordinatesArray.length; i++){
                    var coor = coordinatesArray[i];
                    var coor_x = coor.split(" ")[0];
                    var coor_y = coor.split(" ")[1];
                    var coor_x_new = parseFloat(coor_x) + parseFloat(dx) + "";
                    var coor_y_new = parseFloat(coor_y) + parseFloat(dy) + "";
                    coordinates_new = coordinates_new + coor_x_new + " " + coor_y_new;
                    if(i != coordinatesArray.length - 1){
                        coordinates_new = coordinates_new + ",";
                    }
                }
                coordinates_new = coordinates_new + ")";
                tarDHtml.setAttribute("coordinates", coordinates_new);
                updateCoordById(tarId, coordinates_new, null, label_newx, label_newy);
            }
            else if(mouseDownType == "label" || mouseDownType == "label_tspan"){
                var tarId = cobj.attr('id');

                if(tarId.endsWith("_label")) {
                    var diaGeoPsrId_l = tarId.split("_")[0];
                    // --- 处理图元量测标签随图元拖动而联动 ---
                    var diaMeasPId = diaGeoPsrId_l + "_meas_p";
                    var diaMeasPHtml = document.getElementById(diaMeasPId);
                } else if(tarId.endsWith("_meas_p") || tarId.endsWith("_meas_q") || tarId.endsWith("_vm_pu") || tarId.endsWith("_v_a")) {
                    var diaGeoPsrId_l = tarId.split("_")[0];
                    tarId = diaGeoPsrId_l + "_label";
                }

                var tarDHtml = document.getElementById(tarId);

                var label_newx = tarDHtml.getAttribute("x");
                var label_newy = tarDHtml.getAttribute("y");
                var labelType = tarDHtml.getAttribute("labelType");

                updateLabelCoordByLabelId(tarId, label_newx, label_newy, labelType);
            }
            else if(mouseDownType == "black") {
                // console.log("拉框 - 结束");
                mouseDownType = '';	//鼠标抬起时置点击类型置空
                if(zoomDrag){
                    // console.log("拉框 - 结束 zoomDrag:" + zoomDrag);
                    /**
                     * 在这段代码中，this 指的是触发了 zoomEnd 函数的元素。
                     * 具体来说，当该函数被调用时，this 将指向调用该函数的元素。这通常是由事件处理程序调用的函数，在这种情况下，它将指向触发事件的元素。
                     * 例如，如果 zoomEnd 函数是通过以下方式调用的：<div onmouseup="zoomEnd(event)">...</div>，则在函数内部，this 将指向这个 <div> 元素。
                     */

                    var boxDoc = document.getElementById("zoomRect");
                    var box = boxDoc.getBoundingClientRect();	//文档中的位置
                    var box_left = box.left;
                    var box_right = box.right;
                    var box_top = box.top;
                    var box_bottom = box.bottom;

                    var cnt_use = 0;
                    // console.log("遍历前 - rectSelG.length:" + rectSelG.length);
                    $('#drawPdSvg').find('use').each(function() {
                        cnt_use++;
                        var thisId = this.getAttribute('id');
                        var geoPsrId = this.getAttribute('geoPsrId');
                        var psrType = this.getAttribute('psrType');

                        var thisRect = this.getBoundingClientRect();	//文档中的位置
                        var thisRect_left = thisRect.left;
                        var thisRect_right = thisRect.right;
                        var thisRect_top = thisRect.top;
                        var thisRect_bottom = thisRect.bottom;

                        // console.log("thisRect_left:" + thisRect_left + ", thisRect_right:" + thisRect_right + ", thisRect_top:" + thisRect_top + ", thisRect_bottom:" + thisRect_bottom);

                        if((thisRect_left > box_left) && (thisRect_right < box_right) && (thisRect_top > box_top) && (thisRect_bottom < box_bottom)) {
                            const innerDia = new Object();
                            innerDia.diaId = thisId;
                            innerDia.geoPsrId = geoPsrId;

                            const startX = getInnerDiaStartXYById("x", thisId);
                            const startY = getInnerDiaStartXYById("y", thisId);
                            // console.log("startX:" + startX + " || startY:" + startY);
                            innerDia.startX = startX;
                            innerDia.startY = startY;

                            rectSelG.push(innerDia);

                            // 样式变更
                            if(psrType == "0103"){
                                // 杆塔
                                let diaChild = geoPsrId2DiaMap[geoPsrId];
                                let diaSourceType = diaChild.sourceType;

                                if(diaSourceType == "uat"){
                                    // 变换样式
                                    selectedDiaMap[thisId] = selDia(thisId, dia_uat_tower_sty);
                                    // 改变点击的图元的样式
                                    changeDiaStyle(thisId, dia_uat_tower_sel_sty);

                                } else {
                                    if(inTyNotInUavMap != null && typeof(inTyNotInUavMap) != "undefined" && inTyNotInUavMap[geoPsrId] != null){
                                        // 变换样式
                                        selectedDiaMap[thisId] = selDia(thisId, dia_inTyNotInUat_tower_sty);
                                        // 改变点击的图元的样式
                                        changeDiaStyle(thisId, dia_inTyNotInUat_tower_sel_sty);
                                    } else {
                                        // 变换样式
                                        selectedDiaMap[thisId] = selDia(thisId, dia_tower_sty);
                                        // 改变点击的图元的样式
                                        changeDiaStyle(thisId, dia_tower_sel_sty);
                                    }
                                }

                                // 变换样式
                                // selectedDiaMap[thisId] = selDia(thisId, dia_tower_sty);
                                // 改变点击的图元的样式
                                // changeDiaStyle(thisId, dia_tower_sel_sty);
                            }
                            else {
                                let diaClosed = targetElementSVG.attr('closed');
                                if(diaClosed == false || diaClosed == "false"){
                                    // 变换样式
                                    selectedDiaMap[thisId] = selDia(thisId, dia_common_cut_sty);
                                    // 改变点击的图元的样式
                                    changeDiaStyle(thisId, dia_common_cut_sel_sty);
                                } else {
                                    // 变换样式
                                    selectedDiaMap[thisId] = selDia(thisId, dia_common_sty);
                                    // 改变点击的图元的样式
                                    changeDiaStyle(thisId, dia_common_sel_sty);
                                }
                            }
                            // var thisType = this.getAttribute('class');

                            // console.log("图元" + thisId + "被选中");

                            var diaHtml = document.getElementById(thisId);

                            var diaGeoPsrId = diaHtml.getAttribute("geoPsrId");
                            // console.log("遍历到图元的geoPsrId为" + diaGeoPsrId);

                            var diaLabelId = diaGeoPsrId + "_label";
                            var diaLabelHtml = document.getElementById(diaLabelId);
                            if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null){
                                const labelObj = new Object();
                                labelObj.labelId = diaLabelId;
                                labelObj.label_x = diaLabelHtml.getAttribute("x");
                                labelObj.label_y = diaLabelHtml.getAttribute("y");
                                startPoint_relLabelMap[diaLabelId] = labelObj;

                                // startPoint_relLabel.x = diaLabelHtml.getAttribute("x");
                                // startPoint_relLabel.y = diaLabelHtml.getAttribute("y");
                            }

                            // ------- 处理拉框中的图元的虚拟连接线 -- 获取选中的图元所连接的虚拟连接线
                            var relVirtualLines = diaLineReMap[thisId];
                            // console.log("mouseup 拉框 -- relVirtualLines: " + relVirtualLines);
                            // console.log("mouseup - diaLineReMap:  ----- ");
                            // console.log(diaLineReMap);

                            if(relVirtualLines != null && typeof relVirtualLines != undefined) {
                                // console.log(relVirtualLines.length);
                                for(var nn=0; nn<relVirtualLines.length; nn++){
                                    var px;
                                    var py;
                                    var relVirtualLineId = relVirtualLines[nn];
                                    // console.log("选择第" + nn + "个虚拟连接线id为" + relVirtualLineId);
                                    var relVirtualLineHTML = document.getElementById(relVirtualLineId);
                                    if(typeof relVirtualLineHTML != "undefined" && relVirtualLineHTML != null) {
                                        if (!virtualLinePointMap.hasOwnProperty(relVirtualLineId)) {
                                            var virtualLinePointObject = new Object();
                                            virtualLinePointObject.startX = relVirtualLineHTML.getAttribute("x1");
                                            virtualLinePointObject.startY = relVirtualLineHTML.getAttribute("y1");
                                            virtualLinePointObject.endX = relVirtualLineHTML.getAttribute("x2");
                                            virtualLinePointObject.endY = relVirtualLineHTML.getAttribute("y2");
                                            virtualLinePointObject.startPointPsrGeoId = relVirtualLineHTML.getAttribute("startGeoPsrId");
                                            virtualLinePointObject.endPointPsrGeoId = relVirtualLineHTML.getAttribute("endGeoPsrId");
                                            virtualLinePointMap[relVirtualLineId] = virtualLinePointObject;
                                        }
                                    } else {
                                        // 有拐点的线段
                                        var relVirtualLineId_0 = relVirtualLineId + ",0";
                                        var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                        if(relVirtualLine_0_HTML!= null){
                                            if (!virtualLinePointMap.hasOwnProperty(relVirtualLineId)) {
                                                var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                                var vpSize = virtualPoint.split(";").length;
                                                var relVirtualLine_end_HTML = document.getElementById(relVirtualLineId + "," + vpSize);

                                                var virtualLinePointObject = new Object();
                                                virtualLinePointObject.startX = relVirtualLine_0_HTML.getAttribute("x1");
                                                virtualLinePointObject.startY = relVirtualLine_0_HTML.getAttribute("y1");
                                                virtualLinePointObject.endX = relVirtualLine_end_HTML.getAttribute("x2");
                                                virtualLinePointObject.endY = relVirtualLine_end_HTML.getAttribute("y2");
                                                virtualLinePointObject.startPointPsrGeoId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                                virtualLinePointObject.endPointPsrGeoId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");
                                                virtualLinePointMap[relVirtualLineId] = virtualLinePointObject;
                                            }
                                        }
                                    }

                                }

                                // console.log("virtualLinePointMap: ---------------------------- ");
                                // console.log(virtualLinePointMap);
                            }
                        }


                    });
                    // console.log("全图共找到" + cnt_use + "个use图元");

                    var cnt_line = 0;
                    $('#drawPdSvg').find('line').each(function() {
                        cnt_line++;

                        var thisId = this.getAttribute('id');
                        var thisGeoPsrId = this.getAttribute('geoPsrId');

                        var thisRect = this.getBoundingClientRect();	//文档中的位置
                        var thisRect_left = thisRect.left;
                        var thisRect_right = thisRect.right;
                        var thisRect_top = thisRect.top;
                        var thisRect_bottom = thisRect.bottom;

                        var LinePsrType= this.getAttribute('psrType');

                        if((LinePsrType == "0311") && (thisRect_left > box_left) && (thisRect_right < box_right)
                            && (thisRect_top > box_top) && (thisRect_bottom < box_bottom)) {
                            const innerDia_line = new Object();
                            innerDia_line.diaId = thisId;

                            // console.log("thisId:" + thisId);
                            // console.log(thisRect);

                            innerDia_line.geoPsrId = thisGeoPsrId;
                            const startX1 = this.getAttribute('x1');
                            const startY1 = this.getAttribute('y1');
                            const startX2 = this.getAttribute('x2');
                            const startY2 = this.getAttribute('y2');

                            innerDia_line.startX1 = startX1;
                            innerDia_line.startY1 = startY1;
                            innerDia_line.startX2 = startX2;
                            innerDia_line.startY2 = startY2;

                            rectSelG_Line.push(innerDia_line);

                            // 变换样式
                            selectedDiaMap[thisId] = selDia(thisId, busLine_sty);
                            // 改变点击的图元的样式
                            changeDiaStyle(thisId, busLine_sel_sty);


                            var diaLabelId = thisGeoPsrId + "_label";
                            var diaLabelHtml = document.getElementById(diaLabelId);
                            if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null){
                                const labelObj = new Object();
                                labelObj.labelId = diaLabelId;
                                labelObj.label_x = diaLabelHtml.getAttribute("x");
                                labelObj.label_y = diaLabelHtml.getAttribute("y");
                                startPoint_relLabelMap[diaLabelId] = labelObj;

                                // startPoint_relLabel.x = diaLabelHtml.getAttribute("x");
                                // startPoint_relLabel.y = diaLabelHtml.getAttribute("y");
                            }


                            //获取选中的图元所连接的虚拟连接线
                            var relVirtualLines = diaLineReMap[thisId];
                            // console.log("mousedown -- relVirtualLines: " + relVirtualLines);
                            var diaGeoPsrId = this.getAttribute('geoPsrId');
                            // console.log("选中图元的geoPsrId为" + diaGeoPsrId);
                            // console.log(relVirtualLines.length);
                            for(var nn=0; nn<relVirtualLines.length; nn++){
                                var px;
                                var py;
                                var relVirtualLineId = relVirtualLines[nn];
                                // console.log("选择第" + nn + "个虚拟连接线id为" + relVirtualLineId);
                                var relVirtualLineHTML = document.getElementById(relVirtualLineId);

                                if(typeof relVirtualLineHTML != "undefined" && relVirtualLineHTML != null) {
                                    // 没有拐点
                                    var virLineStartGeoPsrId = relVirtualLineHTML.getAttribute("startGeoPsrId");
                                    var virLineEndGeoPsrId = relVirtualLineHTML.getAttribute("endGeoPsrId");

                                    virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                    virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];

                                    if(virLineStartGeoPsrId == diaGeoPsrId){    //开始节点连接
                                        px = relVirtualLineHTML.getAttribute("x1");
                                        py = relVirtualLineHTML.getAttribute("y1");
                                    } else if(virLineEndGeoPsrId == diaGeoPsrId){
                                        px = relVirtualLineHTML.getAttribute("x2");
                                        py = relVirtualLineHTML.getAttribute("y2");
                                    }

                                    // virtualLinePointMap[relVirtualLineId] = px + "," + py;


                                    if (!virtualLinePointMap.hasOwnProperty(relVirtualLineId)) {
                                        var virtualLinePointObject = new Object();
                                        virtualLinePointObject.startX = relVirtualLineHTML.getAttribute("x1");
                                        virtualLinePointObject.startY = relVirtualLineHTML.getAttribute("y1");
                                        virtualLinePointObject.endX = relVirtualLineHTML.getAttribute("x2");
                                        virtualLinePointObject.endY = relVirtualLineHTML.getAttribute("y2");
                                        virtualLinePointObject.startPointPsrGeoId = relVirtualLineHTML.getAttribute("startGeoPsrId");
                                        virtualLinePointObject.endPointPsrGeoId = relVirtualLineHTML.getAttribute("endGeoPsrId");
                                        virtualLinePointMap[relVirtualLineId] = virtualLinePointObject;
                                    }
                                } else {
                                    // 考虑是不是有拐点的线段
                                    var relVirtualLineId_0 = virtualLineId + ",0";
                                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                    if (relVirtualLine_0_HTML != null) {
                                        var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                        var vpSize = virtualPoint.split(";").length;
                                        var relVirtualLine_end_HTML = document.getElementById(virtualLineId + "," + vpSize);
                                        // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                                        var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                        var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                                        virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                        virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];

                                        if(virLineStartGeoPsrId == diaGeoPsrId){    //开始节点连接
                                            px = relVirtualLine_0_HTML.getAttribute("x1");
                                            py = relVirtualLine_0_HTML.getAttribute("y1");
                                        } else if(virLineEndGeoPsrId == diaGeoPsrId){
                                            px = relVirtualLine_end_HTML.getAttribute("x2");
                                            py = relVirtualLine_end_HTML.getAttribute("y2");
                                        }

                                        if (!virtualLinePointMap.hasOwnProperty(relVirtualLineId)) {
                                            var virtualLinePointObject = new Object();
                                            virtualLinePointObject.startX = relVirtualLine_0_HTML.getAttribute("x1");
                                            virtualLinePointObject.startY = relVirtualLine_0_HTML.getAttribute("y1");
                                            virtualLinePointObject.endX = relVirtualLine_end_HTML.getAttribute("x2");
                                            virtualLinePointObject.endY = relVirtualLine_end_HTML.getAttribute("y2");
                                            virtualLinePointObject.startPointPsrGeoId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                            virtualLinePointObject.endPointPsrGeoId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");
                                            virtualLinePointMap[relVirtualLineId] = virtualLinePointObject;
                                        }
                                    }
                                }

                            }

                        }


                    });
                    // console.log("全图共找到" + cnt_line + "个line图元");

                    // console.log("zoom ---end--- box_left:" + box_left + ", box_right:" + box_right + ", box_top:" + box_top + ", box_bottom:" + box_bottom);

                    // 处理 拐点
                    var cnt_virtualPoint = 0;
                    // 处理 母线连接点
                    var cnt_busPoint = 0;
                    $('#drawPdSvg').find('circle').each(function() {
                        var thisId = this.getAttribute('id');
                        var nType = this.getAttribute('type');
                        if(nType == "virtualPoint"){
                            cnt_virtualPoint++;
                            var thisRect = this.getBoundingClientRect();	//文档中的位置
                            var thisRect_left = thisRect.left;
                            var thisRect_right = thisRect.right;
                            var thisRect_top = thisRect.top;
                            var thisRect_bottom = thisRect.bottom;

                            var startX = this.getAttribute('cx');
                            var startY = this.getAttribute('cy');

                            if((thisRect_left > box_left) && (thisRect_right < box_right) && (thisRect_top > box_top) && (thisRect_bottom < box_bottom)) {
                                const innerDia = new Object();
                                innerDia.diaId = thisId;
                                innerDia.startX = startX;
                                innerDia.startY = startY;
                                rectSelG_VirtualPoint.push(innerDia);

                                // 变换样式
                                selectedDiaMap[thisId] = selDia(thisId, virtualNode_sty);
                                // 改变点击的图元的样式
                                changeDiaStyle(thisId, virtualNode_sel_sty);
                            }
                        }
                        else if(nType == "busPoint"){
                            // 母线连接点
                            cnt_virtualPoint++;
                            var thisRect = this.getBoundingClientRect();	//文档中的位置
                            var thisRect_left = thisRect.left;
                            var thisRect_right = thisRect.right;
                            var thisRect_top = thisRect.top;
                            var thisRect_bottom = thisRect.bottom;

                            var startX = this.getAttribute('cx');
                            var startY = this.getAttribute('cy');

                            if((thisRect_left > box_left) && (thisRect_right < box_right) && (thisRect_top > box_top) && (thisRect_bottom < box_bottom)) {
                                const innerDia = new Object();
                                innerDia.diaId = thisId;
                                innerDia.startX = startX;
                                innerDia.startY = startY;
                                rectSelG_busPoint.push(innerDia);

                                // 变换样式
                                selectedDiaMap[thisId] = selDia(thisId, busPoint_sty);
                                // 改变点击的图元的样式
                                changeDiaStyle(thisId, busPoint_sel_sty);
                            }
                        }

                    });
                    // console.log("全图共找到" + cnt_virtualPoint + "个拐点");
                    // console.log("rectSelG_VirtualPoint ------------ ");
                    // console.log(rectSelG_VirtualPoint);

                    // 重置起始点和结束点
                    zoomStartPoint = null;
                    zoomEndPoint = null;

                    zoomDisX = 0;
                    zoomDisY = 0;



                }
            }
            else if(mouseDownType == "zoomMove"){
                var dx=(event.clientX-deltax) / currentScale;
                var dy=(event.clientY-deltay) / currentScale;

                var toUpdateDia = [];
                var toUpdateBlc = [];   //待更新的母线上的连接点信息
                var toUpdateLine = [];  //待更新的线路（线路拐点）

                // console.log("rectSelG.length:" + rectSelG.length);
                if(rectSelG.length != 0) {
                    // console.log("拉框选中拖拽事件...处理选中图元坐标保存及图元HTML属性更新...");

                    // console.log(rectSelG);
                    for(var n=0; n<rectSelG.length; n++){
                        // TODO 改变图元样式

                        var innerDia = rectSelG[n];
                        var pelId = innerDia.diaId;
                        var pelDoc = document.getElementById(pelId);

                        var gElement = $('#' + pelId);

                        var coordinates = gElement.attr('coordinates');
                        // console.log("mouseup - coordinates:" + coordinates);

                        var coordinateString = coordinates.substring(coordinates.indexOf("(") + 1, coordinates.indexOf(")"));
                        // 分割坐标字符串为两组x和y
                        var coordinatesArray = coordinateString.split(" ");
                        var coordX = coordinatesArray[0];
                        var coordY = coordinatesArray[1];

                        var coordX_new = parseFloat(coordX) + parseFloat(dx);
                        var coordY_new = parseFloat(coordY) + parseFloat(dy);

                        var coordinates_new = "POINT(" + coordX_new + " " + coordY_new + ")";
                        // console.log("coordinates_new:" + coordinates_new);

                        var diaT = new Object();

                        var diaGeoPsrId = pelDoc.getAttribute("geoPsrId");
                        var diaLabelId = diaGeoPsrId + "_label";
                        var diaLabelHtml = document.getElementById(diaLabelId);

                        if (diaLabelHtml) {
                            var label_x = diaLabelHtml.getAttribute("x");
                            var label_y = diaLabelHtml.getAttribute("y");

                            diaT.labelX = label_x;
                            diaT.labelY = label_y;

                            // 鼠标抬起后, 也需要将标签的信息更新到变量集合里
                            const labelObj = new Object();
                            labelObj.labelId = diaLabelId;
                            labelObj.label_x = diaLabelHtml.getAttribute("x");
                            labelObj.label_y = diaLabelHtml.getAttribute("y");
                            startPoint_relLabelMap[diaLabelId] = labelObj;
                        } else {
                            // console.log("diaLabelId: " + diaLabelId + " not found in the DOM.");
                        }

                        diaT.id = pelId;
                        diaT.coordinates_new = coordinates_new;
                        // diaT.labelX = label_x;
                        // diaT.labelY = label_y;

                        toUpdateDia.push(diaT);

                        gElement.attr("coordinates", coordinates_new);

                        // 更新全局变量里图元的coordinates
                        geoPsrId2DiaMap[diaGeoPsrId].coordinates = coordinates_new;

                        // 更新
                        rectSelG[n].startX = parseFloat(innerDia.startX) + parseFloat(dx);
                        rectSelG[n].startY = parseFloat(innerDia.startY) + parseFloat(dy);

                        var relVirtualLines = diaLineReMap[pelId];
                        // console.log("mouseup 拉框 -- relVirtualLines: " + relVirtualLines);

                        if(relVirtualLines != null && typeof relVirtualLines != undefined) {
                            // console.log(relVirtualLines.length);
                            for (var nn = 0; nn < relVirtualLines.length; nn++) {
                                var px;
                                var py;
                                var relVirtualLineId = relVirtualLines[nn];
                                // console.log("选择第" + nn + "个虚拟连接线id为" + relVirtualLineId);
                                var relVirtualLineHTML = document.getElementById(relVirtualLineId);

                                if(typeof relVirtualLineHTML != "undefined" && relVirtualLineHTML != null) {
                                    // 没有拐点
                                    var virtualLinePointObject = new Object();
                                    virtualLinePointObject.startX = relVirtualLineHTML.getAttribute("x1");
                                    virtualLinePointObject.startY = relVirtualLineHTML.getAttribute("y1");
                                    virtualLinePointObject.endX = relVirtualLineHTML.getAttribute("x2");
                                    virtualLinePointObject.endY = relVirtualLineHTML.getAttribute("y2");
                                    virtualLinePointObject.startPointPsrGeoId = relVirtualLineHTML.getAttribute("startGeoPsrId");
                                    virtualLinePointObject.endPointPsrGeoId = relVirtualLineHTML.getAttribute("endGeoPsrId");
                                    virtualLinePointMap[relVirtualLineId] = virtualLinePointObject;
                                } else {
                                    // 考虑是不是有拐点的线段
                                    var relVirtualLineId_0 = relVirtualLineId + ",0";
                                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                    if(relVirtualLine_0_HTML!= null){
                                        var virtualPointStr = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                        var vpSize = virtualPointStr.split(";").length;
                                        var relVirtualLine_end_HTML = document.getElementById(relVirtualLineId + "," + vpSize);
                                        // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2

                                        var virtualLinePointObject = new Object();
                                        virtualLinePointObject.startX = relVirtualLine_0_HTML.getAttribute("x1");
                                        virtualLinePointObject.startY = relVirtualLine_0_HTML.getAttribute("y1");
                                        virtualLinePointObject.endX = relVirtualLine_end_HTML.getAttribute("x2");
                                        virtualLinePointObject.endY = relVirtualLine_end_HTML.getAttribute("y2");
                                        virtualLinePointObject.startPointPsrGeoId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                        virtualLinePointObject.endPointPsrGeoId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");
                                        virtualLinePointMap[relVirtualLineId] = virtualLinePointObject;
                                    }
                                }

                            }
                        }

                    }
                    // console.log(rectSelG);


                }

                if(rectSelG_Line.length != 0) {
                    // console.log("拉框选中拖拽事件...处理选中母线图元坐标保存及图元HTML属性更新...");

                    // console.log(rectSelG_Line);
                    for (var n = 0; n < rectSelG_Line.length; n++) {
                        // TODO 改变图元样式

                        var innerDia = rectSelG_Line[n];
                        var pelId = innerDia.diaId;
                        var pelDoc = document.getElementById(pelId);

                        var gElement = $('#' + pelId);

                        var coordinates = gElement.attr('coordinates');
                        // console.log("mouseup - coordinates:" + coordinates);

                        var tHeadIndex = coordinates.indexOf('LINESTRING(');
                        var tEndIndex = coordinates.indexOf(')');
                        var coordinates_s = coordinates.substring(tHeadIndex + 11, tEndIndex);
                        var xy = coordinates_s.split(',');
                        var x1 = xy[0].split(' ')[0];
                        var y1 = xy[0].split(' ')[1];
                        var x2 = xy[1].split(' ')[0];
                        var y2 = xy[1].split(' ')[1];

                        var x1_new = parseFloat(x1) + parseFloat(dx);
                        var y1_new = parseFloat(y1) + parseFloat(dy);
                        var x2_new = parseFloat(x2) + parseFloat(dx);
                        var y2_new = parseFloat(y2) + parseFloat(dy);
                        var coordinates_new = "LINESTRING(" + x1_new + " " + y1_new + "," + x2_new + " " + y2_new + ")";
                        // console.log("coordinates_new:" + coordinates_new);

                        var diaT = new Object();

                        var diaGeoPsrId = pelDoc.getAttribute("geoPsrId");
                        var diaLabelId = diaGeoPsrId + "_label";
                        var diaLabelHtml = document.getElementById(diaLabelId);

                        if (diaLabelHtml) {
                            var label_x = diaLabelHtml.getAttribute("x");
                            var label_y = diaLabelHtml.getAttribute("y");

                            diaT.labelX = label_x;
                            diaT.labelY = label_y;

                            // 鼠标抬起后, 也需要将标签的信息更新到变量集合里
                            const labelObj = new Object();
                            labelObj.labelId = diaLabelId;
                            labelObj.label_x = diaLabelHtml.getAttribute("x");
                            labelObj.label_y = diaLabelHtml.getAttribute("y");
                            startPoint_relLabelMap[diaLabelId] = labelObj;
                        } else {
                            console.log("diaLabelId: " + diaLabelId + "  在HTML中未找到图元");
                        }

                        diaT.id = pelId;
                        diaT.coordinates_new = coordinates_new;
                        // diaT.labelX = label_x;
                        // diaT.labelY = label_y;

                        toUpdateDia.push(diaT);

                        gElement.attr("coordinates", coordinates_new);

                        // 更新全局变量里图元的coordinates
                        geoPsrId2DiaMap[diaGeoPsrId].coordinates = coordinates_new;

                        // 更新
                        // rectSelG[n].startX = parseFloat(innerDia.startX) + parseFloat(dx);
                        // rectSelG[n].startY = parseFloat(innerDia.startY) + parseFloat(dy);

                        // rectSelG_Line[n].startX1 = parseFloat(innerDia.startX1) + parseFloat(dx);
                        // rectSelG_Line[n].startY1 = parseFloat(innerDia.startY1) + parseFloat(dy);
                        // rectSelG_Line[n].startX2 = parseFloat(innerDia.startX2) + parseFloat(dx);
                        // rectSelG_Line[n].startY2 = parseFloat(innerDia.startY2) + parseFloat(dy);

                        rectSelG_Line[n].startX1 = x1_new;
                        rectSelG_Line[n].startY1 = y1_new;
                        rectSelG_Line[n].startX2 = x2_new;
                        rectSelG_Line[n].startY2 = y2_new;


                        var relVirtualLines = diaLineReMap[pelId];
                        // console.log("mouseup 拉框 -- relVirtualLines: " + relVirtualLines);

                        if(relVirtualLines != null && typeof relVirtualLines != undefined) {
                            // console.log(relVirtualLines.length);
                            for (var nn = 0; nn < relVirtualLines.length; nn++) {
                                var px;
                                var py;
                                var relVirtualLineId = relVirtualLines[nn];
                                // console.log("选择第" + nn + "个虚拟连接线id为" + relVirtualLineId);
                                var relVirtualLineHTML = document.getElementById(relVirtualLineId);
                                if(typeof relVirtualLineHTML != "undefined" && relVirtualLineHTML != null) {
                                    // 没有拐点
                                    var virLineStartGeoPsrId = relVirtualLineHTML.getAttribute("startGeoPsrId");
                                    var virLineEndGeoPsrId = relVirtualLineHTML.getAttribute("endGeoPsrId");

                                    var virtualLinePointObject = new Object();
                                    virtualLinePointObject.startX = relVirtualLineHTML.getAttribute("x1");
                                    virtualLinePointObject.startY = relVirtualLineHTML.getAttribute("y1");
                                    virtualLinePointObject.endX = relVirtualLineHTML.getAttribute("x2");
                                    virtualLinePointObject.endY = relVirtualLineHTML.getAttribute("y2");
                                    virtualLinePointObject.startPointPsrGeoId = relVirtualLineHTML.getAttribute("startGeoPsrId");
                                    virtualLinePointObject.endPointPsrGeoId = relVirtualLineHTML.getAttribute("endGeoPsrId");
                                    virtualLinePointMap[relVirtualLineId] = virtualLinePointObject;
                                } else {
                                    // 考虑是不是有拐点的线段
                                    var relVirtualLineId_0 = virtualLineId + ",0";
                                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                    if (relVirtualLine_0_HTML != null) {
                                        var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                        var vpSize = virtualPoint.split(";").length;
                                        var relVirtualLine_end_HTML = document.getElementById(virtualLineId + "," + vpSize);
                                        // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                                        var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                        var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                                        var virtualLinePointObject = new Object();
                                        virtualLinePointObject.startX = relVirtualLine_0_HTML.getAttribute("x1");
                                        virtualLinePointObject.startY = relVirtualLine_0_HTML.getAttribute("y1");
                                        virtualLinePointObject.endX = relVirtualLine_end_HTML.getAttribute("x2");
                                        virtualLinePointObject.endY = relVirtualLine_end_HTML.getAttribute("y2");
                                        virtualLinePointObject.startPointPsrGeoId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                        virtualLinePointObject.endPointPsrGeoId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");
                                        virtualLinePointMap[relVirtualLineId] = virtualLinePointObject;
                                    }
                                }


                                //知道拖动母线时带动的虚拟连接线的ID
                                //通过虚拟连接线ID,找到与母线连接的点,将点的圆心也设置为newX1和newY1
                                // console.log("virtualLineId:" + virtualLineId);
                                var mpId = relVirtualLineId + "&" + diaGeoPsrId;
                                var busLineConnPoint = virLine2BusPointMap[mpId];
                                // console.log("busLineConnPoint --------------");
                                // console.log(busLineConnPoint);
                                var busLineConnPointId = busLineConnPoint.id;
                                var busLineConnPointHtml = document.getElementById(busLineConnPointId);

                                var gElement = $('#' + busLineConnPointId);

                                // var cx = gElement.attr('cx');
                                // var cy = gElement.attr('cy');
                                var cx = busLineConnPointHtml.getAttribute('cx');
                                var cy = busLineConnPointHtml.getAttribute('cy');

                                // var cx_new = parseFloat(cx) + parseFloat(dx);
                                // var cy_new = parseFloat(cy) + parseFloat(dy);
                                var cx_new = parseFloat(cx);
                                var cy_new = parseFloat(cy);

                                var diaT = new Object();
                                diaT.id = virLine2BusPointMap[mpId].id;
                                diaT.pointX = cx_new;
                                diaT.pointY = cy_new;
                                toUpdateBlc.push(diaT);

                                // 更新
                                virLine2BusPointMap[mpId].pointX = cx_new;
                                virLine2BusPointMap[mpId].pointY = cy_new;

                            }
                        }

                    }
                }

                if(rectSelG_VirtualPoint != 0) {
                    for(var n=0; n<rectSelG_VirtualPoint.length; n++) {
                        var innerDia = rectSelG_VirtualPoint[n];
                        var pelId = innerDia.diaId;


                        var pelDoc = document.getElementById(pelId);

                        var x_new = pelDoc.getAttribute("cx");
                        var y_new = pelDoc.getAttribute("cy");

                        var diaT = new Object();

                        // ngpg13511134@@25bc4f95-5609-4491-8ea2-b03500ffb50b_node,1
                        var szlineId = pelId.substring(0, pelId.indexOf("_node"));
                        var nodeNum = pelId.split(",")[1];

                        var szLineHTML = document.getElementById(szlineId + ",0");
                        var lineVirtualPoint = szLineHTML.getAttribute("virtualPoint");

                        var lineVirtualPointArray = lineVirtualPoint.split(";");
                        var lineVirtualPointArray_new = "";

                        for(var iii=0; iii<lineVirtualPointArray.length; iii++) {
                            if(iii == nodeNum){
                                lineVirtualPointArray_new = lineVirtualPointArray_new + x_new + "," + y_new + ";";
                            } else {
                                lineVirtualPointArray_new = lineVirtualPointArray_new + lineVirtualPointArray[iii] + ";";
                            }
                        }
                        if(lineVirtualPointArray_new.endsWith(";")){
                            lineVirtualPointArray_new = lineVirtualPointArray_new.substring(0, lineVirtualPointArray_new.length-1);
                        }

                        diaT.id = szlineId;
                        diaT.virtualPoint = lineVirtualPointArray_new;

                        toUpdateLine.push(diaT);

                        // 更新
                        rectSelG_VirtualPoint[n].startX = x_new;
                        rectSelG_VirtualPoint[n].startY = y_new;

                    }
                }

                // console.log("toUpdateBlc---------");
                // console.log(toUpdateBlc);

                // console.log("toUpdateLine---------");
                // console.log(toUpdateLine);

                // ------ 加蒙白 -------
                let pendingRequests = 3;  // 初始待处理请求的数量
                let index = layer.load(0, {shade: [0.3,'#fff']});  // 创建一个加载层

                function onRequestComplete() {
                    pendingRequests--;
                    if (pendingRequests === 0) {
                        layer.close(index);  // 关闭加载层
                    }
                }

                updateZoomCoordById(toUpdateDia, onRequestComplete);
                updateZoomBlcById(toUpdateBlc, onRequestComplete);
                updateZoomVirtualPoint(toUpdateLine, onRequestComplete);

                // ---------------------- 调整站房坐标 -------------------------
                var selDiaTarIds = "";
                for(var n=0; n<rectSelG.length; n++) {
                    var innerDia = rectSelG[n];
                    var pelId = innerDia.diaId;
                    if(n == 0){
                        selDiaTarIds = pelId;
                    } else {
                        selDiaTarIds = selDiaTarIds + "," + pelId;
                    }
                }

                for(var n=0; n<rectSelG_Line.length; n++) {
                    var innerDia = rectSelG_Line[n];
                    var pelId = innerDia.diaId;
                    if(n == 0){
                        selDiaTarIds = pelId;
                    } else {
                        selDiaTarIds = selDiaTarIds + "," + pelId;
                    }
                }

                updateZfAutoSize(selDiaTarIds);
            }
            else if(mouseDownType == "LINEFRAME"){
                // console.log("线框拖动结束");
                // 计算新的坐标
                var dx = (event.clientX - startPoint.x) / currentScale;
                var dy = (event.clientY - startPoint.y) / currentScale;

                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                var diaGeoPsrId = tarDHtml.getAttribute("geoPsrId");
                var zfPsrId = tarDHtml.getAttribute("psrId");
                // console.log("zfPsrId=" + zfPsrId);

                // 2、调整coordinates
                // LINESTRING(10440.451416015625 2355.42529296875,10680.451416015625 2355.42529296875,10680.451416015625 2480.67529296875,10440.451416015625 2480.67529296875,10440.451416015625 2355.42529296875)
                var coordinates = tarDHtml.getAttribute("coordinates");
                var coordinateString = coordinates.substring(coordinates.indexOf("(") + 1, coordinates.indexOf(")"));
                // 分割坐标字符串为两组（或多组）x和y
                var coordinatesArray = coordinateString.split(",");
                var coordinates_new = "LINESTRING(";
                for(var i=0; i<coordinatesArray.length; i++){
                    var coor = coordinatesArray[i];
                    var coor_x = coor.split(" ")[0];
                    var coor_y = coor.split(" ")[1];
                    var coor_x_new = parseFloat(coor_x) + parseFloat(dx) + "";
                    var coor_y_new = parseFloat(coor_y) + parseFloat(dy) + "";
                    coordinates_new = coordinates_new + coor_x_new + " " + coor_y_new;
                    if(i != coordinatesArray.length - 1){
                        coordinates_new = coordinates_new + ",";
                    }
                }
                coordinates_new = coordinates_new + ")";
                tarDHtml.setAttribute("coordinates", coordinates_new);

                var label_newx = "";
                var label_newy = "";
                // ------------------- 处理图元标签随图元拖动而联动 -----------------
                var diaLabelId = diaGeoPsrId + "_label";
                var diaLabelHtml = document.getElementById(diaLabelId);
                // console.log("diaLabelHtml --------------------- ");
                // console.log(diaLabelHtml);
                if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                    // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                    label_newx = parseFloat(startPoint_relLabel.x) + parseFloat(dx);
                    label_newy = parseFloat(startPoint_relLabel.y) + parseFloat(dy);
                }

                // 3、将线框的coordinates更新数据库
                updateCoordById(tarId, coordinates_new, null, label_newx, label_newy);

                // 4、更新站房内所有子图元坐标
                var zfIDGeoPsrIds = diagramStationMap[zfPsrId];

                var toUpdateDia = [];
                var toUpdateBlc = [];   //待更新的母线上的连接点信息

                for(var i=0; i<zfIDGeoPsrIds.length; i++){
                    var IDGeoPsrId = zfIDGeoPsrIds[i];
                    console.log("IDGeoPsrId:" + IDGeoPsrId);
                    var IDia = geoPsrId2DiaMap[IDGeoPsrId];
                    // console.log(IDia);
                    var IDiaId = IDia.id;
                    var IDiaHtml = document.getElementById(IDiaId);

                    if(IDiaHtml == null || IDiaHtml == "" || typeof IDiaHtml == "undefined"){
                        console.log("站房内图元" + IDiaHtml + "不存在，已被合并");
                        continue;
                    }

                    var IDiacoordinates = IDia.coordinates;  // 依赖于每次操作后全局变量的更新
                    // var IDiacoordinates = IDiaHtml.getAttribute("coordinates");  // 依赖于每次操作后对界面HTML属性的更新
                    var gElement = $('#' + IDiaId);
                    if(IDiacoordinates.startsWith("POINT(")){
                        var diaT = new Object();
                        // 图元类型
                        var transformAttr = gElement.attr('transform');
                        console.log("transformAttr:" + transformAttr);
                        var tHeadIndex = transformAttr.indexOf('translate(');
                        var tHead = transformAttr.substr(0, (tHeadIndex + 10));
                        var remain = transformAttr.substr((tHeadIndex + 10), transformAttr.length);
                        var tEndIndex = remain.indexOf(')');
                        var tEnd = remain.substr(tEndIndex, remain.length);
                        var xy = remain.substr(0, tEndIndex);
                        // console.log("xy:" + xy);
                        var xyArray = xy.split(" ");
                        var x_old = xyArray[0];
                        var y_old = xyArray[1];

                        var x_new = parseFloat(x_old) + parseFloat(dx);
                        var y_new = parseFloat(y_old) + parseFloat(dy);

                        var newTransform = tHead + x_new + " " + y_new + tEnd;

                        gElement.attr('transform', newTransform);
                        // console.log("newTransform:" + newTransform);

                        // -- 更新coordinates
                        var coordinateString = IDiacoordinates.substring(IDiacoordinates.indexOf("(") + 1, IDiacoordinates.indexOf(")"));
                        // console.log("coordinateString:" + coordinateString);
                        // 分割坐标字符串为两组x和y
                        var coordinatesArray = coordinateString.split(" ");
                        var coordX = coordinatesArray[0];
                        var coordY = coordinatesArray[1];
                        var coordX_new = parseFloat(coordX) + parseFloat(dx);
                        var coordY_new = parseFloat(coordY) + parseFloat(dy);
                        var newCoordinates = "POINT(" + coordX_new + " " + coordY_new + ")";
                        diaT.coordinates_new = newCoordinates;
                        gElement.attr('coordinates', newCoordinates);
                        // console.log("newCoordinates:" + newCoordinates);


                        // 更新全局变量里图元的coordinates
                        geoPsrId2DiaMap[IDGeoPsrId].coordinates = newCoordinates;



                        // ------------------------------------------------ 还需要调整图元的连接线相关

                        // ------------ 处理图元标签随图元拖动而联动 -----------
                        var diaLabelId = IDGeoPsrId + "_label";
                        var diaLabelHtml = document.getElementById(diaLabelId);

                        if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {
                            var label_x_old = diaLabelHtml.getAttribute("x");
                            var label_y_old = diaLabelHtml.getAttribute("y");
                            var label_newx = parseFloat(label_x_old) + parseFloat(dx);
                            var label_newy = parseFloat(label_y_old) + parseFloat(dy);
                            diaLabelHtml.setAttribute("x", label_newx);
                            diaLabelHtml.setAttribute("y", label_newy);

                            diaT.labelX = label_newx;
                            diaT.labelY = label_newy;

                            if (diaLabelHtml.children.length > 0) {
                                // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                                // 所有孩子节点，移动
                                for (let i = 0; i < diaLabelHtml.children.length; i++) {
                                    const childNode = diaLabelHtml.children[i];
                                    if (childNode.nodeName.toLowerCase() === 'tspan') {
                                        // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                        // 更新x坐标
                                        childNode.setAttribute('x', label_newx);
                                        childNode.setAttribute('y', parseFloat(label_newy) + i*10);
                                    } else {
                                        // 不是tspan节点，按需处理或忽略
                                    }
                                }
                            } else {
                                if(typeof diaLabelHtml != "undefined" && diaLabelHtml != null) {

                                }
                            }
                        }

                        // --- 处理图元量测标签随图元拖动而联动 ---
                        var diaMeasPId = IDGeoPsrId + "_meas_p";
                        var diaMeasPHtml = document.getElementById(diaMeasPId);
                        if(typeof diaMeasPHtml != "undefined" && diaMeasPHtml != null) {
                            var label_x_old = diaMeasPHtml.getAttribute("x");
                            var label_y_old = diaMeasPHtml.getAttribute("y");
                            var label_newx = parseFloat(label_x_old) + parseFloat(dx);
                            var label_newy = parseFloat(label_y_old) + parseFloat(dy);

                            diaMeasPHtml.setAttribute("x", label_newx);
                            diaMeasPHtml.setAttribute("y", label_newy);
                        }
                        var diaMeasQId = IDGeoPsrId + "_meas_q";
                        var diaMeasQHtml = document.getElementById(diaMeasQId);
                        if(typeof diaMeasQHtml != "undefined" && diaMeasQHtml != null) {
                            var label_x_old = diaMeasQHtml.getAttribute("x");
                            var label_y_old = diaMeasQHtml.getAttribute("y");
                            var label_newx = parseFloat(label_x_old) + parseFloat(dx);
                            var label_newy = parseFloat(label_y_old) + parseFloat(dy);

                            diaMeasQHtml.setAttribute("x", label_newx);
                            diaMeasQHtml.setAttribute("y", label_newy);
                        }

                        // --------------------- 调整相关连接线 -----------------------------
                        var relVirtualLines = diaLineReMap[IDiaId];

                        if(relVirtualLines != null && typeof relVirtualLines != undefined) {
                            for (var m = 0; m < relVirtualLines.length; m++) {
                                var virtualLineId = relVirtualLines[m];
                                // console.log("当前处理:" + virtualLineId);
                                var virtualLineHtml = document.getElementById(virtualLineId);
                                // console.log(virtualLineHtml);




                                if(typeof virtualLineHtml != "undefined" && virtualLineHtml != null) {
                                    // 没有拐点
                                    var virLineStartGeoPsrId = virtualLineHtml.getAttribute("startGeoPsrId");
                                    var virLineEndGeoPsrId = virtualLineHtml.getAttribute("endGeoPsrId");

                                    // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                    virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                    virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                    // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                                    if(virLineStartGeoPsrId == IDGeoPsrId){    //开始节点连接
                                        const vlx = virtualLineHtml.getAttribute("x1");
                                        const vly = virtualLineHtml.getAttribute("y1");
                                        const newX1 = (parseFloat(vlx) + parseFloat(dx));
                                        const newY1 = (parseFloat(vly) + parseFloat(dy));

                                        virtualLineHtml.setAttribute("x1", newX1);
                                        virtualLineHtml.setAttribute("y1", newY1);
                                    } else if(virLineEndGeoPsrId == IDGeoPsrId){
                                        const vlx = virtualLineHtml.getAttribute("x2");
                                        const vly = virtualLineHtml.getAttribute("y2");
                                        const newX1 = (parseFloat(vlx) + parseFloat(dx));
                                        const newY1 = (parseFloat(vly) + parseFloat(dy));

                                        virtualLineHtml.setAttribute("x2", newX1);
                                        virtualLineHtml.setAttribute("y2", newY1);

                                    } else{
                                        console.log("没找到");
                                    }
                                } else {
                                    // 考虑是不是有拐点的线段
                                    var relVirtualLineId_0 = virtualLineId + ",0";
                                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                    if(relVirtualLine_0_HTML!= null){
                                        var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                        var vpSize = virtualPoint.split(";").length;
                                        var relVirtualLine_end_HTML = document.getElementById(virtualLineId + "," + vpSize);
                                        // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                                        var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                        var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                                        // console.log("连接线开始节点ID:" + virLineStartGeoPsrId + " || 结束节点ID:" + virLineEndGeoPsrId);
                                        virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                        virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];
                                        // console.log("连接线开始节点主图元ID:" + virLineStartGeoPsrId + " || 结束节点主图元ID:" + virLineEndGeoPsrId);

                                        if(virLineStartGeoPsrId == IDGeoPsrId){    //开始节点连接
                                            const vlx = relVirtualLine_0_HTML.getAttribute("x1");
                                            const vly = relVirtualLine_0_HTML.getAttribute("y1");
                                            const newX1 = (parseFloat(vlx) + parseFloat(dx));
                                            const newY1 = (parseFloat(vly) + parseFloat(dy));

                                            relVirtualLine_0_HTML.setAttribute("x1", newX1);
                                            relVirtualLine_0_HTML.setAttribute("y1", newY1);
                                        } else if(virLineEndGeoPsrId == IDGeoPsrId){
                                            const vlx = relVirtualLine_end_HTML.getAttribute("x2");
                                            const vly = relVirtualLine_end_HTML.getAttribute("y2");
                                            const newX1 = (parseFloat(vlx) + parseFloat(dx));
                                            const newY1 = (parseFloat(vly) + parseFloat(dy));

                                            relVirtualLine_end_HTML.setAttribute("x2", newX1);
                                            relVirtualLine_end_HTML.setAttribute("y2", newY1);
                                        } else{
                                            console.log("没找到");
                                        }

                                        /*// 处理拐点位置移动
                                        for(var nn=0; nn<vpSize; nn++){
                                            var vNode_HTML = document.getElementById(virtualLineId + "_node," + nn);
                                            if(vNode_HTML!= null){
                                                var vNode_x = vNode_HTML.getAttribute("cx");
                                                var vNode_y = vNode_HTML.getAttribute("cy");
                                                var newX = (parseFloat(vNode_x) + parseFloat(dx));
                                                var newY = (parseFloat(vNode_y) + parseFloat(dy));
                                                vNode_HTML.setAttribute("cx", newX);
                                                vNode_HTML.setAttribute("cy", newY);


                                                // TODO 还需要调整拐点两端的线段的对应开始/结束坐标

                                            }
                                        }*/

                                    }

                                }

                            }
                        }

                        diaT.id = IDiaId;

                        toUpdateDia.push(diaT);
                    }
                    else if(IDiacoordinates.startsWith("LINESTRING(")) {
                        var diaT = new Object();
                        const startX1 = IDiaHtml.getAttribute('x1');
                        const startY1 = IDiaHtml.getAttribute('y1');
                        const startX2 = IDiaHtml.getAttribute('x2');
                        const startY2 = IDiaHtml.getAttribute('y2');

                        const startX1_new = parseFloat(startX1) + parseFloat(dx);
                        const startY1_new = parseFloat(startY1) + parseFloat(dy);
                        const startX2_new = parseFloat(startX2) + parseFloat(dx);
                        const startY2_new = parseFloat(startY2) + parseFloat(dy);

                        gElement.attr('x1', startX1_new);
                        gElement.attr('y1', startY1_new);
                        gElement.attr('x2', startX2_new);
                        gElement.attr('y2', startY2_new);

                        // -------- 标签 --------
                        var mxGeoPsrId = IDiaHtml.getAttribute("geoPsrId");
                        var mxLabelId = mxGeoPsrId + "_label";
                        var mxLabelHtml = document.getElementById(mxLabelId);

                        if (typeof mxLabelHtml != "undefined" && mxLabelHtml != null) {
                            var mx_label_x = mxLabelHtml.getAttribute("x");
                            var mx_label_y = mxLabelHtml.getAttribute("y");

                            var mx_label_x_new = parseFloat(mx_label_x) + dx;
                            var mx_label_y_new = parseFloat(mx_label_y) + dy;

                            var dhcl = 0;
                            if(mxLabelHtml.children == null || typeof(mxLabelHtml.children) == "undefined"){
                                dhcl = 0;
                            } else {
                                dhcl = mxLabelHtml.children.length;
                            }

                            if (mxLabelHtml.children != null && typeof(mxLabelHtml.children) != "undefined" && mxLabelHtml.children.length > 0) {
                                // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                                // 所有孩子节点，移动
                                for (let i = 0; i < mxLabelHtml.children.length; i++) {
                                    const childNode = mxLabelHtml.children[i];
                                    if (childNode.nodeName.toLowerCase() === 'tspan') {
                                        // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                                        // 更新x坐标
                                        childNode.setAttribute('x', mx_label_x_new);
                                        // childNode.setAttribute('y', parseFloat(label_newy) - (diaLabelHtml.children.length - i) * 10 + 20);
                                        childNode.setAttribute('y', parseFloat(mx_label_y_new) + i*labelWrapHeight);
                                        // childNode.setAttribute("y", parseFloat(label_newy) + i * 10);
                                    } else {
                                        // 不是tspan节点，按需处理或忽略
                                    }
                                }
                                mxLabelHtml.setAttribute("x", mx_label_x_new);
                                mxLabelHtml.setAttribute("y", mx_label_y_new);
                            } else {
                                if(typeof mxLabelHtml != "undefined" && mxLabelHtml != null) {
                                    // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                                    mxLabelHtml.setAttribute("x", mx_label_x_new);
                                    mxLabelHtml.setAttribute("y", mx_label_y_new);
                                }
                            }




                            mxLabelHtml.setAttribute("x", mx_label_x_new);
                            mxLabelHtml.setAttribute("y", mx_label_y_new);

                            diaT.labelX = mx_label_x_new;
                            diaT.labelY = mx_label_y_new;
                        }

                        var relVirtualLines = diaLineReMap[IDiaId];
                        // console.log("线框中母线 relVirtualLines: " + relVirtualLines);

                        if(relVirtualLines != null && typeof relVirtualLines != undefined) {
                            for (var n = 0; n < relVirtualLines.length; n++) {
                                var virtualLineId = relVirtualLines[n];
                                var virtualLineHtml = document.getElementById(virtualLineId);

                                if(typeof virtualLineHtml != "undefined" && virtualLineHtml != null) {
                                    var virLineStartGeoPsrId = virtualLineHtml.getAttribute("startGeoPsrId");
                                    var virLineEndGeoPsrId = virtualLineHtml.getAttribute("endGeoPsrId");

                                    virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                    virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];

                                    var vlx = virtualLineHtml.getAttribute("x1");
                                    var vly = virtualLineHtml.getAttribute("y1");

                                    var newX1 = (parseFloat(vlx) + parseFloat(dx));
                                    var newY1 = (parseFloat(vly) + parseFloat(dy));


                                    if(virLineStartGeoPsrId == IDGeoPsrId){    //开始节点连接
                                        // console.log("if in -----------------");
                                        virtualLineHtml.setAttribute("x1", newX1);
                                        virtualLineHtml.setAttribute("y1", newY1);
                                    } else if(virLineEndGeoPsrId == IDGeoPsrId){
                                        // console.log("else in -----------------");
                                        vlx = virtualLineHtml.getAttribute("x2");
                                        vly = virtualLineHtml.getAttribute("y2");
                                        newX1 = (parseFloat(vlx) + parseFloat(dx));
                                        newY1 = (parseFloat(vly) + parseFloat(dy));

                                        virtualLineHtml.setAttribute("x2", newX1);
                                        virtualLineHtml.setAttribute("y2", newY1);
                                    }
                                } else {
                                    // 考虑是不是有拐点的线段
                                    var relVirtualLineId_0 = virtualLineId + ",0";
                                    var relVirtualLine_0_HTML = document.getElementById(relVirtualLineId_0);
                                    if (relVirtualLine_0_HTML != null) {
                                        var virtualPoint = relVirtualLine_0_HTML.getAttribute("virtualPoint");
                                        var vpSize = virtualPoint.split(";").length;
                                        var relVirtualLine_end_HTML = document.getElementById(virtualLineId + "," + vpSize);
                                        // 找出有拐点的线路的 两端线段，其他图元与这个线路连肯定只能接再 首、尾。若接在首段，则取x1、y1    若接在尾段，则取x2、y2
                                        var virLineStartGeoPsrId = relVirtualLine_0_HTML.getAttribute("startGeoPsrId");
                                        var virLineEndGeoPsrId = relVirtualLine_end_HTML.getAttribute("endGeoPsrId");

                                        virLineStartGeoPsrId = merToMainMap[virLineStartGeoPsrId];
                                        virLineEndGeoPsrId = merToMainMap[virLineEndGeoPsrId];

                                        var vlx = relVirtualLine_0_HTML.getAttribute("x1");
                                        var vly = relVirtualLine_0_HTML.getAttribute("y1");

                                        var newX1 = (parseFloat(vlx) + parseFloat(dx));
                                        var newY1 = (parseFloat(vly) + parseFloat(dy));


                                        if(virLineStartGeoPsrId == IDGeoPsrId){    //开始节点连接
                                            // console.log("if in -----------------");
                                            relVirtualLine_0_HTML.setAttribute("x1", newX1);
                                            relVirtualLine_0_HTML.setAttribute("y1", newY1);
                                        } else if(virLineEndGeoPsrId == IDGeoPsrId){
                                            // console.log("else in -----------------");
                                            vlx = relVirtualLine_end_HTML.getAttribute("x2");
                                            vly = relVirtualLine_end_HTML.getAttribute("y2");
                                            newX1 = (parseFloat(vlx) + parseFloat(dx));
                                            newY1 = (parseFloat(vly) + parseFloat(dy));

                                            relVirtualLine_end_HTML.setAttribute("x2", newX1);
                                            relVirtualLine_end_HTML.setAttribute("y2", newY1);
                                        }

                                    }
                                }

                                // ----------- 当前情况下，拉框选中的线路只有母线，所以需要处理母线上对的点  ----------------
                                //知道拖动母线时带动的虚拟连接线的ID
                                //通过虚拟连接线ID,找到与母线连接的点,将点的圆心也设置为newX1和newY1
                                // console.log("virtualLineId:" + virtualLineId);
                                var mpId = virtualLineId + "&" + mxGeoPsrId;
                                var busLineConnPoint = virLine2BusPointMap[mpId];
                                // console.log("virtualLineId:" + virtualLineId);
                                // console.log("busLineConnPoint --------------");
                                // console.log(busLineConnPoint);
                                if(busLineConnPoint == null || busLineConnPoint == "" || busLineConnPoint == undefined || typeof(busLineConnPoint) == "undefined") {
                                    console.warn("virtualLineId:" + virtualLineId + "对应的母线连接点为空,待验证");
                                    continue;
                                }
                                var busLineConnPointId = busLineConnPoint.id;
                                var busLineConnPointHtml = document.getElementById(busLineConnPointId);
                                busLineConnPointHtml.setAttribute("cx", newX1);
                                busLineConnPointHtml.setAttribute("cy", newY1);

                                var diaBLC = new Object();
                                diaBLC.id = virLine2BusPointMap[mpId].id;
                                diaBLC.pointX = newX1;
                                diaBLC.pointY = newY1;
                                toUpdateBlc.push(diaBLC);
                            }
                        }

                        //coordinates="LINESTRING(1155.6296691894531 689.6715698242188,1960.5471496582031 689.6715698242188)"
                        var coordinateString = IDiacoordinates.substring(IDiacoordinates.indexOf("(") + 1, IDiacoordinates.indexOf(")"));
                        var coordinatesArray = coordinateString.split(",");
                        var x1_old = coordinatesArray[0].split(' ')[0];
                        var y1_old = coordinatesArray[0].split(' ')[1];
                        var x2_old = coordinatesArray[1].split(' ')[0];
                        var y2_old = coordinatesArray[1].split(' ')[1];

                        var x1_new = parseFloat(x1_old) + parseFloat(dx);
                        var y1_new = parseFloat(y1_old) + parseFloat(dy);
                        var x2_new = parseFloat(x2_old) + parseFloat(dx);
                        var y2_new = parseFloat(y2_old) + parseFloat(dy);
                        var newCoordinates = "LINESTRING(" + x1_new + " " + y1_new + "," + x2_new + " " + y2_new + ")";
                        gElement.attr('coordinates', newCoordinates);   // 更新界面中HTML属性
                        geoPsrId2DiaMap[IDGeoPsrId].coordinates = newCoordinates;   // 更新全局变量里图元的coordinates

                        diaT.coordinates_new = newCoordinates;
                        diaT.id = IDiaId;

                        toUpdateDia.push(diaT);
                    }
                }

                // ------ 加蒙白 -------
                let pR_mouseUp_LINEFRAME = 2;  // 初始待处理请求的数量
                let index = layer.load(0, {shade: [0.3,'#fff']});  // 创建一个加载层

                function onRC_mouseUp_LINEFRAME() {
                    pR_mouseUp_LINEFRAME--;
                    if (pR_mouseUp_LINEFRAME === 0) {
                        layer.close(index);  // 关闭加载层
                    }
                }

                updateZoomCoordById(toUpdateDia, onRC_mouseUp_LINEFRAME);

                updateZoomBlcById(toUpdateBlc, onRC_mouseUp_LINEFRAME);

            }
            else if(mouseDownType == "virtualPoint") {
                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                var vp_x_new = tarDHtml.getAttribute("cx");
                var vp_y_new = tarDHtml.getAttribute("cy");

                // ngpg13511134@@25bc4f95-5609-4491-8ea2-b03500ffb50b_node,1
                var szlineId = tarId.substring(0, tarId.indexOf("_node"));
                var nodeNum = tarId.split(",")[1];

                var szLineHTML = document.getElementById(szlineId + ",0");
                var lineVirtualPoint = szLineHTML.getAttribute("virtualPoint");

                var lineVirtualPointArray = lineVirtualPoint.split(";");
                var lineVirtualPointArray_new = "";
                for(var iii=0; iii<lineVirtualPointArray.length; iii++) {
                    if(iii == nodeNum){
                        lineVirtualPointArray_new = lineVirtualPointArray_new + vp_x_new + "," + vp_y_new + ";";
                    } else {
                        lineVirtualPointArray_new = lineVirtualPointArray_new + lineVirtualPointArray[iii] + ";";
                    }
                }
                if(lineVirtualPointArray_new.endsWith(";")){
                    lineVirtualPointArray_new = lineVirtualPointArray_new.substring(0, lineVirtualPointArray_new.length-1);
                }

                // 把主线段（0段）的属性改下
                szLineHTML.setAttribute("virtualPoint", lineVirtualPointArray_new);

                updateLineVirtualPointById(szlineId, lineVirtualPointArray_new);

                // 更新变量
                lineId2LineMap[szlineId].virtualPoint = lineVirtualPointArray_new;
            }
            else if(mouseDownType == "busPoint"){
                var tarId = cobj.attr('id');
                var tarDHtml = document.getElementById(tarId);

                var busPoint_x_new = tarDHtml.getAttribute("cx");
                var busPoint_y_new = tarDHtml.getAttribute("cy");

                // 更新后台数据
                updateBusPointCoord(tarId, busPoint_x_new, busPoint_y_new);
            }
            else {
                // console.log("暂不处理");
            }
            resetPara();
        }
    });

    $("#drawPdSvg").bind("wheel", function(event) {
        // 在这里处理滚轮事件
        var delta = event.originalEvent.wheelDelta || (-event.originalEvent.deltaY);
        if (delta > 0) {
            // console.log('向上滚动');
        } else {
            // console.log('向下滚动');
        }
        // 阻止默认行为，例如页面滚动
        // event.preventDefault();
    });

    window.addEventListener("keydown",keyDown,false);

    $("#drawPdSvg").bind('mouseover', function(event) {
        var targetElement = event.target;
        var tarName = targetElement.tagName;
        var targetElementSVG = SVG.adopt(targetElement);
        var texttype = targetElementSVG.attr('texttype');
        var tarId = targetElementSVG.attr('id');
        var tarClass = targetElementSVG.attr('class');
        // console.log("tarClass: " + tarClass);

        if(tarClass != null && tarClass!= undefined && typeof(tarClass)!= "undefined"){
            if(tarClass == "basicInfo"      // 基础部分
                || tarClass.includes("faultYTST")  // 一图四态故障点
            ){
                return;
            }
        }

        if(selectedDiaMap[tarId] == null) {
            if(tarName.toLowerCase() == "use") {
                // this.style = dia_common_sel_sty;
                var psrType = targetElementSVG.attr('psrType');
                var diaHtml = document.getElementById(tarId);

                if(psrType == "0103"){
                    var geoPsrId = targetElementSVG.attr('geoPsrId');
                    var diaChild = geoPsrId2DiaMap[geoPsrId];
                    var diaSourceType = diaChild.sourceType;

                    if(diaSourceType == "uat"){
                        diaHtml.setAttribute("style", dia_uat_tower_sel_sty);
                    } else {
                        if(inTyNotInUavMap != null && typeof(inTyNotInUavMap) != "undefined" && inTyNotInUavMap[geoPsrId] != null){
                            diaHtml.setAttribute("style", dia_inTyNotInUat_tower_sel_sty);
                        } else {
                            diaHtml.setAttribute("style", dia_tower_sel_sty);
                        }
                    }
                    // diaHtml.setAttribute("style", dia_tower_sel_sty);
                }
                else {
                    let diaClosed = targetElementSVG.attr('closed');
                    if(diaClosed == false || diaClosed == "false"){
                        diaHtml.setAttribute("style", dia_common_cut_sel_sty);
                    } else {
                        diaHtml.setAttribute("style", dia_common_sel_sty);
                    }
                }
            }
            else if(tarName.toLowerCase() == "line") {
                var diaHtml = document.getElementById(tarId);
                var psrType = diaHtml.getAttribute("psrType");
                if(psrType == "0311"){
                    diaHtml.setAttribute("style", busLine_sel_sty);
                }
                var lineType = diaHtml.getAttribute("lineType");

                if(lineType == "virtuallyLine"){
                    var oldStyle = diaHtml.getAttribute("style");
                    // console.log("oldStyle: " + oldStyle);
                    if(oldStyle.includes("dasharray")){
                        // console.log("是虚线");
                        diaHtml.setAttribute("style", virtuallyLine_dashed_sel_sty);
                    } else {
                        diaHtml.setAttribute("style", virtuallyLine_sel_sty);
                    }
                }
            }
            else if(tarName.toLowerCase() == "path") {
                var diaHtml = document.getElementById(tarId);
                diaHtml.setAttribute("style", lineFrame_sel_sty);
            }
            else if(tarName.toLowerCase() == "circle") {
                var diaHtml = document.getElementById(tarId);
                var nType = diaHtml.getAttribute("type");
                if(nType == "busPoint"){
                    diaHtml.setAttribute("style", busPoint_sel_sty);
                } else if(nType == "virtualPoint"){
                    diaHtml.setAttribute("style", virtualNode_sel_sty);
                }
            }
        }

    });
    $("#drawPdSvg").bind('mouseout', function(event) {
        var targetElement = event.target;
        var tarName = targetElement.tagName;
        var targetElementSVG = SVG.adopt(targetElement);
        var texttype = targetElementSVG.attr('texttype');
        var tarId = targetElementSVG.attr('id');
        var tarClass = targetElementSVG.attr('class');

        if(tarClass != null && tarClass!= undefined && typeof(tarClass)!= "undefined"){
            if(tarClass == "basicInfo"      // 基础部分
                || tarClass.includes("faultYTST")  // 一图四态故障点
            ){
                return;
            }
        }

        if(selectedDiaMap[tarId] == null) {
            if(tarName.toLowerCase() == "use") {
                // this.style = dia_common_sty;
                var psrType = targetElementSVG.attr('psrType');
                var diaHtml = document.getElementById(tarId);
                if(psrType == "0103"){
                    var geoPsrId = targetElementSVG.attr('geoPsrId');
                    var diaChild = geoPsrId2DiaMap[geoPsrId];
                    var diaSourceType = diaChild.sourceType;

                    if(diaSourceType == "uat"){
                        diaHtml.setAttribute("style", dia_uat_tower_sty);
                    } else {
                        if(inTyNotInUavMap != null && typeof(inTyNotInUavMap) != "undefined" && inTyNotInUavMap[geoPsrId] != null){
                            diaHtml.setAttribute("style", dia_inTyNotInUat_tower_sty);
                        } else {
                            diaHtml.setAttribute("style", dia_tower_sty);
                        }
                    }
                    // diaHtml.setAttribute("style", dia_tower_sty);
                }
                else {
                    let diaClosed = targetElementSVG.attr('closed');
                    if(diaClosed == false || diaClosed == "false"){
                        diaHtml.setAttribute("style", dia_common_cut_sty);
                    } else {
                        diaHtml.setAttribute("style", dia_common_sty);
                    }
                }
            } else if(tarName.toLowerCase() == "line") {
                var diaHtml = document.getElementById(tarId);
                var psrType = diaHtml.getAttribute("psrType");
                if(psrType == "0311"){
                    diaHtml.setAttribute("style", busLine_sty);
                }
                var lineType = diaHtml.getAttribute("lineType");
                if(lineType == "virtuallyLine"){
                    var oldStyle = diaHtml.getAttribute("style");
                    // console.log("oldStyle: " + oldStyle);
                    if(oldStyle.includes("dasharray")){
                        // console.log("是虚线");
                        diaHtml.setAttribute("style", virtuallyLine_dashed_sty);
                    } else {
                        diaHtml.setAttribute("style", virtuallyLine_sty);
                    }
                }
            } else if(tarName.toLowerCase() == "path") {
                var diaHtml = document.getElementById(tarId);
                diaHtml.setAttribute("style", lineFrame_sty);
            } else if(tarName.toLowerCase() == "circle") {
                var diaHtml = document.getElementById(tarId);
                var nType = diaHtml.getAttribute("type");
                if(nType == "busPoint"){
                    diaHtml.setAttribute("style", busPoint_sty);
                } else if(nType == "virtualPoint"){
                    diaHtml.setAttribute("style", virtualNode_sty);
                }
            }
        }
    });



}

window.updateZfAutoSize = function (tarId) {
    // console.log(tarId);

    var needUpdateZfPsrIds = new Object();
    if(tarId.indexOf(',') != -1){
        // console.log("拉框选择了多个图元");
        var tarIds = tarId.split(",");

        for(var n=0; n<tarIds.length; n++){
            var tarDoc = document.getElementById(tarIds[n]);
            if(typeof tarDoc != "undefined" && tarDoc != null) {
                var tarGeoPsrId = tarDoc.getAttribute("geoPsrId");
                // console.log(tarGeoPsrId);
                var zfPsrId_this = diaToStationMap[tarGeoPsrId];
                needUpdateZfPsrIds[zfPsrId_this] = zfPsrId_this;
                // console.log("needUpdateZfPsrIds:" + needUpdateZfPsrIds);
            } else {
                continue;
            }
        }
    } else {
        // console.log("拉框只选择了一个图元");
        var tarDoc = document.getElementById(tarId);
        if(typeof tarDoc != "undefined" && tarDoc != null) {
            var tarGeoPsrId = tarDoc.getAttribute("geoPsrId");
            // console.log(tarGeoPsrId);
            var zfPsrId_this = diaToStationMap[tarGeoPsrId];
            needUpdateZfPsrIds[zfPsrId_this] = zfPsrId_this;
            // console.log("needUpdateZfPsrIds:" + needUpdateZfPsrIds);
        }
        else {
            console.log("没有选择图元");
            return;
        }

    }

    if(typeof needUpdateZfPsrIds == "undefined" || needUpdateZfPsrIds == null || needUpdateZfPsrIds == ""){
        // 当前拖动的图元不是在站房内的设备
        return;
    }

    for(var key in needUpdateZfPsrIds){
        zfPsrId = key;

        var innerDias = diagramStationMap[zfPsrId];

        if(typeof innerDias == "undefined" || innerDias == null || innerDias == ""){
            continue;
        }

        var x_min = 0;
        var y_min = 0;
        var x_max = 0;
        var y_max = 0;

        // console.log("innerDias.length:" + innerDias.length);
        for(var mn=0; mn<innerDias.length; mn++){
            var diaGeoPsrIds = innerDias[mn];
            var dia = geoPsrId2DiaMap[diaGeoPsrIds];
            var diaId = dia.id;
            // console.log("diaId:" + diaId);
            var diaHTML = document.getElementById(diaId);
            if(diaHTML == null || diaHTML == "" || typeof diaHTML == "undefined"){
                console.log("站房内图元" + diaId + "不存在，已被合并");
                continue;
            }
            var coordinates = diaHTML.getAttribute("coordinates");
            // TODO 这里取diagramStationMap里的图元的coordinates属性，已经未必是最新的了，因为已经有了拖动，暂时先通过id取html里的值。后续考虑如何在操作后实时更新
            // var coordinates = dia.coordinates;

            if(coordinates != undefined && coordinates != "" && typeof coordinates != "undefined"){
                if(coordinates.startsWith("POINT")){
                    var coordinateString = coordinates.substring(coordinates.indexOf("(") + 1, coordinates.indexOf(")"));
                    // console.log(coordinateString);
                    var x = coordinateString.split(" ")[0];
                    var y = coordinateString.split(" ")[1];
                    if(mn == 0){
                        x_min = parseFloat(x);
                        x_max = parseFloat(x);
                        y_min = parseFloat(y);
                        y_max = parseFloat(y);
                    } else {
                        if(parseFloat(x) < x_min){
                            x_min = parseFloat(x);
                        } else if(parseFloat(x) > x_max){
                            x_max = parseFloat(x);
                        }
                        if(parseFloat(y) < y_min){
                            y_min = parseFloat(y);
                        } else if(parseFloat(y) > y_max){
                            y_max = parseFloat(y);
                        }
                    }
                } else if(coordinates.startsWith("LINE")){
                    var coordinateString = coordinates.substring(coordinates.indexOf("(") + 1, coordinates.indexOf(")"));
                    // 分割坐标字符串为两组（或多组）x和y
                    // 分割坐标字符串为x和y
                    const coordinatesArray = coordinateString.split(",");
                    for(var ii=0; ii<coordinatesArray.length; ii++){
                        // 提取x和y坐标
                        const x = coordinatesArray[ii].split(" ")[0];
                        const y = coordinatesArray[ii].split(" ")[1];
                        if(mn == 0){
                            if(ii == 0){
                                x_min = parseFloat(x);
                                x_max = parseFloat(x);
                                y_min = parseFloat(y);
                                y_max = parseFloat(y);
                            } else {
                                x_min = Math.min(parseFloat(x), x_min);
                                x_max = Math.max(parseFloat(x), x_max);
                                y_min = Math.min(parseFloat(y), y_min);
                                y_max = Math.max(parseFloat(y), y_max);
                            }
                        } else {
                            x_min = Math.min(parseFloat(x), x_min);
                            x_max = Math.max(parseFloat(x), x_max);
                            y_min = Math.min(parseFloat(y), y_min);
                            y_max = Math.max(parseFloat(y), y_max);
                        }
                    }


                }
            }
        }

        // ----- 这里值要跟后端保持一致 -----
        x_min = x_min - 30;
        x_max = x_max + 30;
        y_min = y_min - 20;
        y_max = y_max + 30;

        // console.log("x_min:" + x_min + " x_max:" + x_max + " y_min:" + y_min + " y_max:" + y_max);

        var zfId = getElementIdByPsrId('psrId', zfPsrId);

        var zfHtml = document.getElementById(zfId);
        if(typeof zfHtml != "undefined" && zfHtml != null) {
            var newCoordinates = "LINESTRING(" + x_min + " " + y_min + "," + x_max + " " + y_min + "," + x_max + " " + y_max + "," + x_min + " " + y_max + "," + x_min + " " + y_min + ")";
            // 左上 - 右上 - 右下 - 左下 - 左上
            var newPath = "M" + x_min + " " + y_min + "L" + x_max + " " + y_min + "L" + x_max + " " + y_max + "L" + x_min + " " + y_max + "L" + x_min + " " + y_min + " Z";
            zfHtml.setAttribute("d", newPath);

            var newCoordinates = "LINESTRING(" + x_min + " " + y_min + "," + x_max + " " + y_min + "," + x_max + " " + y_max + "," + x_min + " " + y_max + "," + x_min + " " + y_min + ")";
            zfHtml.setAttribute("coordinates", newCoordinates);

            // 拉框拖动时，站房的形状可能发生变化，标签重新计算位置，放置到站房上边靠左
            var label_x_new = x_min;
            var label_y_new = y_min - 20;   // 这里-20要跟后端自动生成时对应一致

            // --- 处理图元标签随图元拖动而联动 ---
            var zfGeoPsrId = zfHtml.getAttribute("geoPsrId");
            var zfLabelId = zfGeoPsrId + "_label";
            var zfLabelHtml = document.getElementById(zfLabelId);

            if(typeof zfLabelHtml != "undefined" && zfLabelHtml != null) {
                var dhcl = 0;
                if(zfLabelHtml.children == null || typeof(zfLabelHtml.children) == "undefined"){
                    dhcl = 0;
                } else {
                    dhcl = zfLabelHtml.children.length;
                }

                if (zfLabelHtml.children != null && typeof(zfLabelHtml.children) != "undefined" && zfLabelHtml.children.length > 0) {
                    // 如果有孩子节点，即当前其实点击的是tspan，在点击事件时已经处理取父节点text
                    // 所有孩子节点，移动
                    for (let i = 0; i < zfLabelHtml.children.length; i++) {
                        const childNode = zfLabelHtml.children[i];
                        if (childNode.nodeName.toLowerCase() === 'tspan') {
                            // 获取当前tspan的dy值（注意dy可能是字符串，需要转换为数值）
                            // 更新x坐标
                            childNode.setAttribute('x', label_x_new);
                            // childNode.setAttribute('y', parseFloat(label_newy) - (diaLabelHtml.children.length - i) * 10 + 20);
                            childNode.setAttribute('y', parseFloat(label_y_new) + i*labelWrapHeight);
                            // childNode.setAttribute("y", parseFloat(label_newy) + i * 10);
                        } else {
                            // 不是tspan节点，按需处理或忽略
                        }
                    }
                    zfLabelHtml.setAttribute("x", label_x_new);
                    zfLabelHtml.setAttribute("y", label_y_new);
                } else {
                    if(typeof zfLabelHtml != "undefined" && zfLabelHtml != null) {
                        // console.log("startPoint_relLabel.x:" + startPoint_relLabel.x + " || startPoint_relLabel.y:" + startPoint_relLabel.y);
                        zfLabelHtml.setAttribute("x", label_x_new);
                        zfLabelHtml.setAttribute("y", label_y_new);
                    }
                }
            }

            updateCoordById(zfId, newCoordinates, "", label_x_new, label_y_new);
        }
    }

}

// 通过图元html id获取图元拉框后该图元初始的 transform 的x和y, 第一个参数传入x获取x, 传入y获取y
window.getInnerDiaStartXYById = function (xoy, thisId) {
    var gElement = $('#' + thisId);
    var transformAttr = gElement.attr('transform');
    // console.log("transformAttr:" + transformAttr);

    //transformAttr:translate(188.8503646850586 115.76722717285156) rotate(0 13.5 3.5)
    //tHead:translate(
    //transformAttr.length():66
    //remain:188.8503646850586 115.76722717285156) rotate(0 13.5 3.5)
    //xy:188.8503646850586 115.76722717285156
    //tEnd: rotate(0 13.5 3.5)

    var tHeadIndex = transformAttr.indexOf('translate(');

    // console.log("tHeadIndex:" + tHeadIndex);
    var tHead = transformAttr.substr(0, (tHeadIndex + 10));
    // console.log("tHead:" + tHead);
    // console.log("transformAttr.length():" + transformAttr.length);
    var remain = transformAttr.substr((tHeadIndex + 10), transformAttr.length);
    // console.log("remain:" + remain);

    var tEndIndex = remain.indexOf(')');
    var xy = remain.substr(0, tEndIndex);
    // console.log("xy:" + xy);
    var tEnd = remain.substr(tEndIndex, remain.length);
    // console.log("tEnd:" + tEnd);

    var x = 0;
    var y = 0;

    //处理transform的xy,取出x和y,并分别加偏移值
    if(xy.indexOf(' ') != -1){
        var xyArray = xy.split(" ");
        x = xyArray[0];
        y = xyArray[1];
    } else if(xy.indexOf(',') != -1){
        var xyArray = xy.split(" ");
        x = xyArray[0];
        y = xyArray[1];
    } else {
        console.log("截取的transformAttr有问题 transformAttr：" + transformAttr + ", 返回(0,0)");
    }

    if(xoy == "x"){
        return x;
    } else if(xoy == "y") {
        return y;
    }
}



