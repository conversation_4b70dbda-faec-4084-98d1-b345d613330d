<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>父页面示例 - 方案三</title>
    <link rel="stylesheet" href="./js/lib/layui/css/layui.css">
    <script src="./js/lib/layui/layui.all.js"></script>
    <script src="./js/drawJs/jquery-2.2.4.min.js"></script>
</head>
<body>
    <div style="padding: 20px;">
        <h2>父页面示例 - 方案三（自定义标题栏）</h2>
        <p>方案三：隐藏layui弹窗标题栏，在内容区域顶部创建自定义标题栏</p>
        <button id="openBinaryTree" class="layui-btn">打开二叉图（无标题栏）</button>
        <button id="openBinaryTreeDirect" class="layui-btn layui-btn-normal">直接打开页面</button>
    </div>

    <script>
        layui.use(['layer'], function(){
            var layer = layui.layer;
            var binaryTreeWindow = null;

            // 监听来自iframe的消息
            window.addEventListener('message', function(event) {
                if (event.data && event.data.action) {
                    switch(event.data.action) {
                        case 'minimize':
                            if (binaryTreeWindow) {
                                layer.min(binaryTreeWindow);
                            }
                            break;
                        case 'maximize':
                            if (binaryTreeWindow) {
                                layer.full(binaryTreeWindow);
                            }
                            break;
                        case 'close':
                            if (binaryTreeWindow) {
                                layer.close(binaryTreeWindow);
                            }
                            break;
                    }
                }
            });

            // 打开二叉图弹窗（无标题栏）
            $('#openBinaryTree').click(function() {
                var psrId = '123'; // 示例ID
                
                binaryTreeWindow = layer.open({
                    type: 2,
                    title: false, // 隐藏标题栏
                    area: ['90%', '90%'],
                    shade: 0,
                    maxmin: true,
                    resize: true,
                    content: ['binary_tree.html?psrId=' + psrId],
                    success: function(layero, index) {
                        console.log('弹窗已打开，使用自定义标题栏');
                    },
                    end: function () {
                        binaryTreeWindow = null;
                    }
                });
            });

            // 直接打开页面
            $('#openBinaryTreeDirect').click(function() {
                window.open('binary_tree.html', '_blank');
            });
        });
    </script>
</body>
</html>
