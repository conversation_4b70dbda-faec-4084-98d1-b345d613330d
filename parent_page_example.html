<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>父页面示例 - 方案三</title>
    <link rel="stylesheet" href="./js/lib/layui/css/layui.css" />
    <script src="./js/lib/layui/layui.all.js"></script>
    <script src="./js/drawJs/jquery-2.2.4.min.js"></script>
  </head>
  <body>
    <div style="padding: 20px">
      <h2>父页面示例 - 方案三（自定义标题栏）</h2>
      <p>方案三：隐藏layui弹窗标题栏，在内容区域顶部创建自定义标题栏</p>
      <button id="openBinaryTree" class="layui-btn">
        打开二叉图（无标题栏）
      </button>
      <button id="openBinaryTreeDirect" class="layui-btn layui-btn-normal">
        直接打开页面
      </button>
    </div>

    <script>
      layui.use(["layer"], function () {
        var layer = layui.layer;
        var binaryTreeWindow = null;
        var binaryTreeFrame = null;

        // 创建标题栏HTML
        function createTitleToolbar() {
          return `
                    <div class="title-toolbar">
                        <div class="title-text">二叉图</div>
                        <div class="title-toolbar-actions">
                            <!-- 布局控制 -->
                            <button class="title-toolbar-btn active" id="title-btn-layout-lr" title="左右布局">
                                <i class="layui-icon layui-icon-next"></i>横向
                            </button>
                            <button class="title-toolbar-btn" id="title-btn-layout-tb" title="上下布局">
                                <i class="layui-icon layui-icon-down"></i>纵向
                            </button>
                            <button class="title-toolbar-btn" id="title-btn-layout-rl" title="右左布局">
                                <i class="layui-icon layui-icon-prev"></i>反向
                            </button>
                            
                            <div class="title-toolbar-separator"></div>
                            
                            <!-- 图形操作 -->
                            <button class="title-toolbar-btn" id="title-btn-fit" title="适应窗口">
                                <i class="layui-icon layui-icon-screen-full"></i>适应
                            </button>
                            <button class="title-toolbar-btn" id="title-btn-reset" title="重置位置">
                                <i class="layui-icon layui-icon-refresh-3"></i>重置
                            </button>
                            
                            <div class="title-toolbar-separator"></div>
                            
                            <!-- 节点操作 -->
                            <button class="title-toolbar-btn" id="title-btn-search" title="搜索节点">
                                <i class="layui-icon layui-icon-search"></i>搜索
                            </button>
                            <button class="title-toolbar-btn" id="title-btn-highlight" title="高亮路径">
                                <i class="layui-icon layui-icon-light"></i>高亮
                            </button>
                            
                            <div class="title-toolbar-separator"></div>
                            
                            <!-- 工具功能 -->
                            <button class="title-toolbar-btn" id="title-btn-export" title="导出图片">
                                <i class="layui-icon layui-icon-download-circle"></i>导出
                            </button>
                        </div>
                    </div>
                `;
        }

        // 绑定工具栏事件
        function bindToolbarEvents() {
          // 布局切换
          $("#title-btn-layout-lr").click(function () {
            if (!$(this).hasClass("active")) {
              binaryTreeFrame.toolbarSwitchLayout("LR");
              $('.title-toolbar-btn[id^="title-btn-layout-"]').removeClass(
                "active"
              );
              $(this).addClass("active");
            }
          });

          $("#title-btn-layout-tb").click(function () {
            if (!$(this).hasClass("active")) {
              binaryTreeFrame.toolbarSwitchLayout("UD");
              $('.title-toolbar-btn[id^="title-btn-layout-"]').removeClass(
                "active"
              );
              $(this).addClass("active");
            }
          });

          $("#title-btn-layout-rl").click(function () {
            if (!$(this).hasClass("active")) {
              binaryTreeFrame.toolbarSwitchLayout("RL");
              $('.title-toolbar-btn[id^="title-btn-layout-"]').removeClass(
                "active"
              );
              $(this).addClass("active");
            }
          });

          // 图形操作
          $("#title-btn-fit").click(function () {
            binaryTreeFrame.toolbarFit();
          });

          $("#title-btn-reset").click(function () {
            binaryTreeFrame.toolbarReset();
          });

          // 节点操作
          $("#title-btn-search").click(function () {
            binaryTreeFrame.toolbarSearch();
          });

          $("#title-btn-highlight").click(function () {
            binaryTreeFrame.toolbarHighlight();
            // 更新按钮状态
            var isActive = binaryTreeFrame.getHighlightMode();
            if (isActive) {
              $(this).addClass("active");
            } else {
              $(this).removeClass("active");
            }
          });

          // 工具功能
          $("#title-btn-export").click(function () {
            binaryTreeFrame.toolbarExport();
          });
        }

        // 二叉树准备完成回调
        window.onBinaryTreeReady = function () {
          console.log("二叉树已准备完成，可以使用工具栏功能");
        };

        // 打开二叉图弹窗
        $("#openBinaryTree").click(function () {
          var psrId = "123"; // 示例ID

          binaryTreeWindow = layer.open({
            type: 2,
            title: createTitleToolbar(), // 使用自定义标题栏
            area: ["90%", "90%"],
            shade: 0,
            maxmin: true,
            resize: true,
            content: ["binary_tree.html?psrId=" + psrId],
            success: function (layero, index) {
              // 获取iframe引用
              binaryTreeFrame = layero.find("iframe")[0].contentWindow;

              // 绑定工具栏事件
              bindToolbarEvents();

              console.log("弹窗已打开，工具栏已绑定");
            },
            end: function () {
              binaryTreeWindow = null;
              binaryTreeFrame = null;
            },
          });
        });
      });
    </script>
  </body>
</html>
