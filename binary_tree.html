<!--当前使用的-->
<html>
<head>
    <link rel="stylesheet" href="./js/lib/layui/css/layui.css">
    <link rel="stylesheet" href="./css/pdSvg.css">
    <script src="./js/lib/layui/layui.all.js"></script>
    <script src="./js/drawJs/jquery-2.2.4.min.js"></script>
    <script src="./js/drawJs/pdMain.js"></script>
    <script src="./js/lib/jquery.contextmenu.r2.js"></script>
    <script src="./js/drawJs/pdSvg/pdCommonPara.js"></script>
    <script src="./js/drawJs/svg.min.js"></script>
    <script src="./js/drawJs/d3.v6.min.js"></script>
    <script src="./js/drawJs/d3-interpolate.v1.min.js"></script>
    <script type="text/javascript" src="./js/lib/vis-network.min.js"></script>

    <style type="text/css">
        #mynetwork {
            width: 100%;
            height: 100%;
            border: 1px solid #ccc;
            background-color: #fff;
            box-sizing: border-box;
        }

        /* 下拉菜单内容区的样式 */
        .dropdown-content {
            padding: 10px 15px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
        }

        .dropdown-content .layui-form-checkbox {
            margin-bottom: 8px;
        }

        .dropdown-content .layui-form-checkbox:last-child {
            margin-bottom: 0;
        }


        /* 右键菜单样式 */
        .custom-context-menu {
            display: none;
            position: absolute;
            background: white;
            border: 1px solid #d4d4d4;
            border-radius: 4px;
            box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.2);
            padding: 5px 0;
            z-index: 1000;
            min-width: 150px;
        }

        .custom-context-menu ul {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .custom-context-menu li {
            padding: 8px 15px;
            cursor: pointer;
            font-size: 14px;
            color: #333;
        }

        .custom-context-menu li:hover {
            background-color: #f0f0f0;
        }

        .custom-context-menu li i {
            margin-right: 8px;
            color: #1E9FFF;
        }

        /* 节点详情弹窗样式 */
        .node-detail-content {
            padding: 15px;
        }

        .node-detail-item {
            margin-bottom: 10px;
            display: flex;
        }

        .node-detail-label {
            width: 100px;
            font-weight: bold;
            color: #555;
        }

        .node-detail-value {
            flex: 1;
            color: #333;
            word-break: break-all;
        }

        /* 复制按钮样式 */
        .copy-btn {
            position: absolute;
            right: 15px;
            top: 15px;
            cursor: pointer;
            color: #1E9FFF;
            font-size: 16px;
        }

        .copy-btn:hover {
            color: #009688;
        }

        /* 复制成功提示 */
        .copy-success {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #5FB878;
            color: white;
            padding: 10px 15px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            z-index: 9999;
            display: none;
        }
    </style>
</head>
<body>

<div id="mynetwork"></div>
<!--<div class="page-wrapper">
    &lt;!&ndash; 1. 自定义的页面头部 &ndash;&gt;
    <div class="page-header">
        <span class="title">二叉树</span>
        <div class="toolbar">
            <i id="tool-settings-btn" class="layui-icon layui-icon-set toolbar-item" title="设置"></i>
        </div>
    </div>
    &lt;!&ndash; 2. 页面内容区 &ndash;&gt;
    <div class="page-content">
        <div id="mynetwork"></div>
    </div>
</div>-->
<div id="customContextMenu" class="custom-context-menu">
    <ul>
        <li onclick="showNodeDetails()"><i class="layui-icon">&#xe60a;</i>查看详情</li>
        <!--        <li onclick="expandNode()"><i class="layui-icon">&#xe623;</i>展开节点</li>
                <li onclick="collapseNode()"><i class="layui-icon">&#xe625;</i>折叠节点</li>-->
        <li onclick="copyNodeInfo()"><i class="layui-icon">&#xe64e;</i>复制信息</li>
    </ul>
</div>

<div id="copySuccess" class="copy-success">
    <i class="layui-icon">&#xe616;</i> 复制成功！
</div>

<script type="text/javascript">
    // 全局变量
    var network = null;
    var selectedNodeId = null;
    var selectedNodeData = null;

    // ===== ① 全局：保存初始坐标 =====
    var initialPositions = {};

    //初始化加载数据
    layui.use(['jquery', 'layer', 'form'], function () {
        var $ = layui.$;
        var layer = layui.layer;
        var form = layui.form;

        var tipsIndex = null;   // 用于存储当前 tips 的索引
        var closeTimer = null;  // 用于存储关闭 tips 的定时器

        // 封装一个关闭 tips 的函数
        function closeTips() {
            if (tipsIndex) {
                layer.close(tipsIndex);
                tipsIndex = null;
            }
        }

        // --- 鼠标进入触发元素 ---
        $('#tool-settings-btn').on('mouseenter', function() {
            // 如果鼠标再次进入，清除可能存在的关闭定时器
            if (closeTimer) {
                clearTimeout(closeTimer);
                closeTimer = null;
            }

            // 如果 tips 已经存在，则不再重复创建
            if (tipsIndex) {
                return;
            }

            var dropdownHtml =
                '<div class="layui-form" lay-filter="binaryTreeForm">' +
                '<div class="dropdown-content">' +
                '<input type="checkbox" name="option1" title="标签选项一" lay-skin="primary" checked>' +
                '<input type="checkbox" name="option2" title="标签选项二" lay-skin="primary">' +
                '<input type="checkbox" name="option3" title="标签选项三" lay-skin="primary">' +
                '</div>' +
                '</div>';

            // 创建 tips 并保存索引
            tipsIndex = layer.tips(dropdownHtml, this, {
                tips: [4, '#fff'],
                time: 0, // 必须为0，手动管理关闭
                // tipsMore: true, // 我们不再需要这个，因为我们自己处理 mouseenter/mouseleave
                skin: 'layui-layer-molv',
                success: function(tipsLayero, index) {
                    // 渲染表单
                    form.render('checkbox', 'binaryTreeForm');

                    // --- 关键：为 tips 层本身也绑定鼠标事件 ---

                    // 鼠标进入 tips 层，取消关闭定时器
                    tipsLayero.on('mouseenter', function() {
                        if (closeTimer) {
                            clearTimeout(closeTimer);
                            closeTimer = null;
                        }
                    });

                    // 鼠标离开 tips 层，启动关闭定时器
                    tipsLayero.on('mouseleave', function() {
                        closeTimer = setTimeout(function() {
                            closeTips();
                        }, 100); // 100毫秒延迟
                    });

                    // 监听多选框事件
                    form.on('checkbox(binaryTreeForm)', function(data){
                        console.log('多选框状态改变:', data.elem.title, '是否选中:', data.elem.checked);
                    });
                }
            });
        });

        // --- 鼠标离开触发元素 ---
        $('#tool-settings-btn').on('mouseleave', function() {
            // 启动一个关闭定时器
            closeTimer = setTimeout(function() {
                closeTips();
            }, 100); // 100毫秒延迟
        });

        // 解析URL参数函数
        function getQueryParams() {
            const params = {};
            const queryString = window.location.search.substring(1);
            const pairs = queryString.split('&');

            for (let i = 0; i < pairs.length; i++) {
                const pair = pairs[i].split('=');
                const key = decodeURIComponent(pair[0]);
                const value = decodeURIComponent(pair[1] || '');
                params[key] = value;
            }

            return params;
        }

        const params = getQueryParams();
        // 获取容器
        var container = document.getElementById('mynetwork');
        $.ajax({
            url: commonUrl.baseUrl + "userTransAnalysis/dataAnalysis0822",
            contentType: "application/x-www-form-urlencoded",
            type: 'get',
            dataType: 'json',
            xhrFields: {
                withCredentials: true
            },
            headers: {
                "Authorization": 'Dwqsk ' + tokens
            },
            data: {
                psrId: params.psrId
            },
            success: function (data) {
                let nodes = [];
                let yijiaruLine = [];
                let dataList = data.dvList;
                let dvList = data.pocessedVLList;
                for (let i = 0; i < dataList.length; i++) {
                    let data = dataList[i];
                    data.label = data.psrTypeName
                    data.data_id = data.id

                    data.shape = "image";
                    let id = data.geoPsrId

                    data.id = id
                    if (data.psrTypeName == undefined || data.psrTypeName == "") {
                        if (data.psrType == "3112") {
                            data.psrTypeName = "计量箱"
                        }
                        if (data.psrType == "3218000") {
                            data.psrTypeName = "低压用户接入点"
                        }
                    }

                    data.image = "./image/AIChat.png";
                    if (data.psrTypeName == "计量箱") {
                        data.image = "./image/align_top.png";
                    }
                    if (data.psrTypeName == "墙支架") {
                        data.image = "./image/larger.png";
                    }
                    if (data.psrTypeName == "低压用户接入点") {
                        data.image = "./image/smaller.png";
                    }
                    if (data.psrTypeName == "柱上变压器（公用变）") {
                        data.image = "./image/tower_all.png";
                    }
                    if (data.psrTypeName == "断路器") {
                        data.image = "./image/tower_ty.png";
                    }


                    // 添加标题用于悬停提示
                    data.title = `ID: ${data.data_id}
                        类型: ${data.psrTypeName}
                        连接: ${data.connection}
                    `;

                    nodes.push(data)

                }


                let edges = [];
                for (let i = 0; i < dvList.length; i++) {
                    let dvlist = dvList[i];
                    edges.push({from: dvlist.startGeoPsrId, to: dvlist.endGeoPsrId})

                    //     edges.push(data)
                }

                console.log("页面显示图元数量=" + nodes.length)

                var nodes1 = new vis.DataSet(nodes)
                var edges1 = new vis.DataSet(edges)
                // 将数据赋值给vis 数据格式化器
                var data = {
                    nodes: nodes1,
                    edges: edges1
                };

                // 配置选项
                var options = {
                    layout: {
                        hierarchical: {
                            enabled: true,
                            direction: 'LR',        // 改为横向（Left → Right）
                            sortMethod: 'hubsize',  // hubsize 一般能减少交叉
                            shakeTowards: 'roots',  // 尽量往根对齐，减少交错
                            nodeSpacing: 220,       // 同层节点间距（可以调大/调小）
                            levelSeparation: 280    // 层与层之间的距离
                        }
                    },
                    nodes: {
                        size: 30,
                        font: {
                            size: 14,
                            face: 'Tahoma'
                        },
                        borderWidth: 2
                    },
                    edges: {
                        width: 2,
                        color: {
                            color: '#848484',
                            highlight: '#555555'
                        },
                        smooth: {
                            enabled: false
                        },
                        arrows: {
                            to: {
                                enabled: true,
                                scaleFactor: 1
                            }
                        }
                    },
                    physics: {
                        enabled: false
                    },
                    interaction: {
                        hover: true,
                        tooltipDelay: 200,
                        navigationButtons: true,
                        keyboard: true,
                        dragNodes: true
                    }
                };


                // 初始化关系图
                network = new vis.Network(container, data, options);

                // 首帧绘制完成后记录所有节点位置
                network.once("afterDrawing", function () {
                    initialPositions = network.getPositions(nodes1.getIds());
                });

                function snapshotPositions() {
                    initialPositions = network.getPositions(nodes1.getIds());
                }

                //拖动结束时，把被拖动的节点移回原位
                network.on("dragEnd", function (params) {
                    if (!params.nodes || params.nodes.length === 0) return;
                    params.nodes.forEach(function (id) {
                        var pos = initialPositions[id];
                        if (pos) network.moveNode(id, pos.x, pos.y);
                    });
                    network.fit({animation: {duration: 300, easingFunction: "easeInOutQuad"}});
                });


                // 添加右键菜单事件
                network.on("oncontext", function (params) {
                    console.log("--- 鼠标右键事件 (oncontext) 触发 ---");
                    console.log("事件参数 (params):", params);
                    params.event.preventDefault();

                    if (params.nodes && params.nodes.length > 0) {
                        selectedNodeId = params.nodes[0];
                        selectedNodeData = nodes1.get(selectedNodeId);

                        console.log("右键点击了节点 ID:", selectedNodeId);

                        var menu = document.getElementById("customContextMenu");
                        menu.style.display = 'block';
                        menu.style.left = params.pointer.DOM.x + 'px';
                        menu.style.top = params.pointer.DOM.y + 'px';
                    } else {
                        selectedNodeId = null;
                        selectedNodeData = null;
                        console.log("右键点击了空白区域。");
                        document.getElementById("customContextMenu").style.display = 'none';
                    }
                });

                // 2. 添加单击事件 (click)
                network.on("click", function (params) {
                    console.log("--- 鼠标单击事件 (click) 触发 ---");
                    console.log("事件参数 (params):", params);
                    document.getElementById("customContextMenu").style.display = 'none';
                    if (params.nodes && params.nodes.length > 0) {
                        var clickedNodeId = params.nodes[0];
                        var clickedNodeData = nodes1.get(clickedNodeId);

                        console.log("单击了节点 ID:", clickedNodeId);
                        console.log("节点数据:", clickedNodeData);
                    } else {
                        console.log("单击了空白区域。");
                    }
                });

                // 3. 添加双击事件 (doubleClick)
                network.on("doubleClick", function (params) {
                    console.log("--- 鼠标双击事件 (doubleClick) 触发 ---");
                    console.log("事件参数 (params):", params);

                    if (params.nodes && params.nodes.length > 0) {
                        selectedNodeId = params.nodes[0];
                        selectedNodeData = nodes1.get(selectedNodeId);

                        console.log("双击了节点 ID:", selectedNodeId, "，准备显示详情。");
                        showNodeDetails();
                    } else {
                        console.log("双击了空白区域。");
                    }
                });
            }
        })

    });

    // 显示节点详情
    function showNodeDetails() {
        if (!selectedNodeData) return;

        // 隐藏右键菜单
        document.getElementById("customContextMenu").style.display = 'none';

        // 使用layui的弹窗
        layer.open({
            type: 1,
            title: '节点详情 - ' + selectedNodeData.label,
            area: ['500px', '400px'],
            content: createNodeDetailContent(selectedNodeData),
            btn: ['关闭'],
            yes: function (index, layero) {
                layer.close(index);
            }
        });
    }

    // 创建节点详情内容
    function createNodeDetailContent(nodeData) {
        let content = `
            <div class="node-detail-content">
                <div class="node-detail-item">
                    <span class="node-detail-label">节点ID:</span>
                    <span class="node-detail-value">${nodeData.id || ''}</span>
                </div>
                <div class="node-detail-item">
                    <span class="node-detail-label">数据ID:</span>
                    <span class="node-detail-value">${nodeData.data_id || ''}</span>
                </div>
                <div class="node-detail-item">
                    <span class="node-detail-label">类型名称:</span>
                    <span class="node-detail-value">${nodeData.psrTypeName || ''}</span>
                </div>
                <div class="node-detail-item">
                    <span class="node-detail-label">连接信息:</span>
                    <span class="node-detail-value">${nodeData.connection || ''}</span>
                </div>
        `;

        // 添加其他可能存在的属性
        for (let key in nodeData) {
            if (!['id', 'data_id', 'psrTypeName', 'connection', 'label', 'title'].includes(key)) {
                content += `
                    <div class="node-detail-item">
                        <span class="node-detail-label" style="word-wrap: break-word">${key}:</span>
                        <span class="node-detail-value">${nodeData[key] || ''}</span>
                    </div>
                `;
            }
        }

        content += `</div>`;
        return content;
    }

    // 复制节点信息
    function copyNodeInfo() {
        if (!selectedNodeData) return;

        // 隐藏右键菜单
        document.getElementById("customContextMenu").style.display = 'none';

        // 构建要复制的文本内容
        let textToCopy = `节点详情信息\n`;
        textToCopy += `============\n`;
        textToCopy += `节点ID: ${selectedNodeData.id || '无'}\n`;
        textToCopy += `数据ID: ${selectedNodeData.data_id || '无'}\n`;
        textToCopy += `类型名称: ${selectedNodeData.psrTypeName || '无'}\n`;
        textToCopy += `连接信息: ${selectedNodeData.connection || '无'}\n`;

        // 添加其他属性
        for (let key in selectedNodeData) {
            if (!['id', 'data_id', 'psrTypeName', 'connection', 'label', 'title'].includes(key)) {
                textToCopy += `${key}: ${selectedNodeData[key] || '无'}\n`;
            }
        }

        // 使用现代 Clipboard API 复制文本
        navigator.clipboard.writeText(textToCopy).then(function () {
            // 显示复制成功提示
            showCopySuccess();
        }).catch(function (err) {
            // 如果 Clipboard API 不可用，使用备用方法
            copyTextFallback(textToCopy);
        });
    }

    // 显示复制成功提示
    function showCopySuccess() {
        var successMsg = document.getElementById('copySuccess');
        successMsg.style.display = 'block';

        // 2秒后自动隐藏
        setTimeout(function () {
            successMsg.style.display = 'none';
        }, 2000);
    }

    // 复制文本的备用方法
    function copyTextFallback(text) {
        // 创建临时textarea元素
        var textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';

        document.body.appendChild(textarea);
        textarea.select();

        try {
            var successful = document.execCommand('copy');
            if (successful) {
                showCopySuccess();
            } else {
                layer.msg('复制失败，请手动复制', {icon: 2});
            }
        } catch (err) {
            layer.msg('复制失败: ' + err, {icon: 2});
        }

        document.body.removeChild(textarea);
    }

    // 点击页面其他地方隐藏菜单
    document.addEventListener('click', function (e) {
        var menu = document.getElementById("customContextMenu");
        if (menu.style.display === 'block' && !menu.contains(e.target)) {
            menu.style.display = 'none';
        }
    });
</script>
</body>
</html>
