<!--当前使用的-->
<html>
  <head>
    <link rel="stylesheet" href="./js/lib/layui/css/layui.css" />
    <link rel="stylesheet" href="./css/pdSvg.css" />
    <script src="./js/lib/layui/layui.all.js"></script>
    <script src="./js/drawJs/jquery-2.2.4.min.js"></script>
    <script src="./js/drawJs/pdMain.js"></script>
    <script src="./js/lib/jquery.contextmenu.r2.js"></script>
    <script src="./js/drawJs/pdSvg/pdCommonPara.js"></script>
    <script src="./js/drawJs/svg.min.js"></script>
    <script src="./js/drawJs/d3.v6.min.js"></script>
    <script src="./js/drawJs/d3-interpolate.v1.min.js"></script>
    <script type="text/javascript" src="./js/lib/vis-network.min.js"></script>

    <style type="text/css">
      /* 页面整体布局 */
      body {
        margin: 0;
        padding: 0;
        height: 100vh;
        overflow: hidden;
      }

      /* 页面容器 */
      .page-container {
        display: flex;
        flex-direction: column;
        height: 100vh;
      }

      /* 自定义标题栏 */
      .custom-title-bar {
        height: 40px;
        background: linear-gradient(to bottom, #fafafa 0%, #e9e9e9 100%);
        border-bottom: 1px solid #d0d0d0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        user-select: none;
        position: relative;
        z-index: 1000;
      }

      /* 标题区域 */
      .title-section {
        display: flex;
        align-items: center;
        gap: 15px;
      }

      .title-text {
        font-size: 14px;
        font-weight: bold;
        color: #333;
        display: flex;
        align-items: center;
        gap: 5px;
      }

      /* 工具栏区域 */
      .toolbar-section {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      /* 下拉标签样式 */
      .dropdown-label {
        position: relative;
        display: inline-block;
      }

      .dropdown-trigger {
        padding: 4px 10px;
        border: 1px solid #c0c0c0;
        background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        color: #333;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 4px;
        height: 26px;
        line-height: 1;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .dropdown-trigger:hover {
        border-color: #1e9fff;
        background: linear-gradient(to bottom, #e6f7ff 0%, #bae7ff 100%);
        color: #1e9fff;
        box-shadow: 0 1px 3px rgba(30, 159, 255, 0.3);
      }

      .dropdown-trigger.active {
        background: linear-gradient(to bottom, #1e9fff 0%, #1890ff 100%);
        border-color: #1890ff;
        color: #fff;
        box-shadow: 0 1px 3px rgba(30, 159, 255, 0.5);
      }

      /* 下拉菜单 */
      .dropdown-menu {
        position: absolute;
        top: 100%;
        left: 0;
        min-width: 220px;
        background: #fff;
        border: 1px solid #d0d0d0;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 2000;
        display: none;
        margin-top: 2px;
      }

      .dropdown-menu.show {
        display: block;
      }

      /* 下拉菜单内容 */
      .dropdown-content {
        padding: 12px;
      }

      /* 下拉菜单分组 */
      .dropdown-section {
        margin-bottom: 15px;
      }

      .dropdown-section:last-of-type {
        margin-bottom: 10px;
      }

      .dropdown-section-title {
        font-size: 12px;
        font-weight: bold;
        color: #666;
        margin-bottom: 8px;
        padding-bottom: 4px;
        border-bottom: 1px solid #f0f0f0;
      }

      /* 重写layui表单样式以适应下拉菜单 */
      .dropdown-menu .layui-form-checkbox,
      .dropdown-menu .layui-form-radio {
        margin: 4px 0;
        font-size: 11px;
      }

      .dropdown-menu .layui-form-checkbox span,
      .dropdown-menu .layui-form-radio span {
        font-size: 11px;
        color: #333;
      }

      .dropdown-menu .layui-form-checkbox:hover,
      .dropdown-menu .layui-form-radio:hover {
        color: #1e9fff;
      }

      /* 操作按钮区域 */
      .dropdown-actions {
        display: flex;
        gap: 8px;
        padding-top: 8px;
        border-top: 1px solid #f0f0f0;
        margin-top: 8px;
      }

      .dropdown-action-btn {
        flex: 1;
        padding: 6px 12px;
        border: 1px solid #d9d9d9;
        background: #fff;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        color: #333;
        transition: all 0.2s;
      }

      .dropdown-action-btn:hover {
        border-color: #1e9fff;
        color: #1e9fff;
      }

      .dropdown-action-btn.secondary {
        background: #f5f5f5;
        color: #666;
      }

      .dropdown-action-btn.secondary:hover {
        background: #e6e6e6;
        border-color: #ccc;
        color: #333;
      }

      .toolbar-btn {
        padding: 4px 10px;
        border: 1px solid #c0c0c0;
        background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
        color: #333;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        gap: 4px;
        height: 26px;
        line-height: 1;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
      }

      .toolbar-btn:hover {
        border-color: #1e9fff;
        background: linear-gradient(to bottom, #e6f7ff 0%, #bae7ff 100%);
        color: #1e9fff;
        box-shadow: 0 1px 3px rgba(30, 159, 255, 0.3);
      }

      .toolbar-btn.active {
        background: linear-gradient(to bottom, #1e9fff 0%, #1890ff 100%);
        border-color: #1890ff;
        color: #fff;
        box-shadow: 0 1px 3px rgba(30, 159, 255, 0.5);
      }

      .toolbar-separator {
        width: 1px;
        height: 20px;
        background: #d0d0d0;
        margin: 0 4px;
      }

      /* 窗口控制按钮 */
      .window-controls {
        display: flex;
        align-items: center;
        gap: 2px;
      }

      .window-btn {
        width: 28px;
        height: 26px;
        border: 1px solid #c0c0c0;
        background: linear-gradient(to bottom, #ffffff 0%, #f0f0f0 100%);
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        color: #666;
        transition: all 0.2s;
      }

      .window-btn:hover {
        background: linear-gradient(to bottom, #e6f7ff 0%, #bae7ff 100%);
        border-color: #1e9fff;
        color: #1e9fff;
      }

      .window-btn.close:hover {
        background: linear-gradient(to bottom, #ff4d4f 0%, #ff7875 100%);
        border-color: #ff4d4f;
        color: #fff;
      }

      /* 图形显示区域 */
      #mynetwork {
        flex: 1;
        width: 100%;
        border: none;
        background-color: #fff;
        box-sizing: border-box;
      }

      /* 下拉菜单内容区的样式 */
      .dropdown-content {
        padding: 10px 15px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }

      .dropdown-content .layui-form-checkbox {
        margin-bottom: 8px;
      }

      .dropdown-content .layui-form-checkbox:last-child {
        margin-bottom: 0;
      }

      /* 右键菜单样式 */
      .custom-context-menu {
        display: none;
        position: absolute;
        background: white;
        border: 1px solid #d4d4d4;
        border-radius: 4px;
        box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.2);
        padding: 5px 0;
        z-index: 1000;
        min-width: 150px;
      }

      .custom-context-menu ul {
        list-style: none;
        margin: 0;
        padding: 0;
      }

      .custom-context-menu li {
        padding: 8px 15px;
        cursor: pointer;
        font-size: 14px;
        color: #333;
      }

      .custom-context-menu li:hover {
        background-color: #f0f0f0;
      }

      .custom-context-menu li i {
        margin-right: 8px;
        color: #1e9fff;
      }

      /* 节点详情弹窗样式 */
      .node-detail-content {
        padding: 15px;
      }

      .node-detail-item {
        margin-bottom: 10px;
        display: flex;
      }

      .node-detail-label {
        width: 100px;
        font-weight: bold;
        color: #555;
      }

      .node-detail-value {
        flex: 1;
        color: #333;
        word-break: break-all;
      }

      /* 复制按钮样式 */
      .copy-btn {
        position: absolute;
        right: 15px;
        top: 15px;
        cursor: pointer;
        color: #1e9fff;
        font-size: 16px;
      }

      .copy-btn:hover {
        color: #009688;
      }

      /* 复制成功提示 */
      .copy-success {
        position: fixed;
        top: 20px;
        right: 20px;
        background: #5fb878;
        color: white;
        padding: 10px 15px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        z-index: 9999;
        display: none;
      }
    </style>
  </head>
  <body>
    <div class="page-container">
      <!-- 自定义标题栏 -->
      <div class="custom-title-bar">
        <!-- 左侧：标题和工具栏 -->
        <div class="title-section">
          <div class="title-text">
            <i class="layui-icon layui-icon-tree"></i>
            二叉图
          </div>

          <div class="toolbar-section">
            <!-- 下拉标签 -->
            <div class="dropdown-label">
              <div class="dropdown-trigger" id="dropdown-trigger">
                <i class="layui-icon layui-icon-more-vertical"></i>
                选项
                <i class="layui-icon layui-icon-down"></i>
              </div>
              <div class="dropdown-menu" id="dropdown-menu">
                <div class="layui-form" lay-filter="dropdownForm">
                  <div class="dropdown-content">
                    <!-- 显示选项 -->
                    <div class="dropdown-section">
                      <div class="dropdown-section-title">显示选项</div>
                      <input
                        type="checkbox"
                        name="showLabels"
                        title="显示节点标签"
                        lay-skin="primary"
                        checked
                      />
                      <input
                        type="checkbox"
                        name="showEdges"
                        title="显示连接线"
                        lay-skin="primary"
                        checked
                      />
                      <input
                        type="checkbox"
                        name="showIcons"
                        title="显示节点图标"
                        lay-skin="primary"
                        checked
                      />
                    </div>

                    <!-- 节点类型筛选 -->
                    <div class="dropdown-section">
                      <div class="dropdown-section-title">节点类型</div>
                      <input
                        type="checkbox"
                        name="nodeType"
                        value="all"
                        title="全部类型"
                        lay-skin="primary"
                        checked
                      />
                      <input
                        type="checkbox"
                        name="nodeType"
                        value="计量箱"
                        title="计量箱"
                        lay-skin="primary"
                        checked
                      />
                      <input
                        type="checkbox"
                        name="nodeType"
                        value="墙支架"
                        title="墙支架"
                        lay-skin="primary"
                        checked
                      />
                      <input
                        type="checkbox"
                        name="nodeType"
                        value="断路器"
                        title="断路器"
                        lay-skin="primary"
                        checked
                      />
                      <input
                        type="checkbox"
                        name="nodeType"
                        value="变压器"
                        title="变压器"
                        lay-skin="primary"
                        checked
                      />
                    </div>

                    <!-- 主题选项 -->
                    <div class="dropdown-section">
                      <div class="dropdown-section-title">主题</div>
                      <input
                        type="radio"
                        name="theme"
                        value="light"
                        title="浅色主题"
                        checked
                      />
                      <input
                        type="radio"
                        name="theme"
                        value="dark"
                        title="深色主题"
                      />
                    </div>

                    <!-- 操作按钮 -->
                    <div class="dropdown-actions">
                      <button
                        type="button"
                        class="dropdown-action-btn"
                        id="apply-settings"
                      >
                        应用设置
                      </button>
                      <button
                        type="button"
                        class="dropdown-action-btn secondary"
                        id="reset-settings"
                      >
                        重置
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="toolbar-separator"></div>

            <!-- 布局控制 -->
            <button
              class="toolbar-btn active"
              id="btn-layout-lr"
              title="左右布局"
            >
              <i class="layui-icon layui-icon-next"></i>横向
            </button>
            <button class="toolbar-btn" id="btn-layout-tb" title="上下布局">
              <i class="layui-icon layui-icon-down"></i>纵向
            </button>
            <button class="toolbar-btn" id="btn-layout-rl" title="右左布局">
              <i class="layui-icon layui-icon-prev"></i>反向
            </button>

            <div class="toolbar-separator"></div>

            <!-- 图形操作 -->
            <button class="toolbar-btn" id="btn-fit" title="适应窗口">
              <i class="layui-icon layui-icon-screen-full"></i>适应
            </button>
            <button class="toolbar-btn" id="btn-reset" title="重置位置">
              <i class="layui-icon layui-icon-refresh-3"></i>重置
            </button>

            <div class="toolbar-separator"></div>

            <!-- 节点操作 -->
            <button class="toolbar-btn" id="btn-search" title="搜索节点">
              <i class="layui-icon layui-icon-search"></i>搜索
            </button>
            <button class="toolbar-btn" id="btn-highlight" title="高亮路径">
              <i class="layui-icon layui-icon-light"></i>高亮
            </button>

            <div class="toolbar-separator"></div>

            <!-- 工具功能 -->
            <button class="toolbar-btn" id="btn-export" title="导出图片">
              <i class="layui-icon layui-icon-download-circle"></i>导出
            </button>
            <button class="toolbar-btn" id="btn-fullscreen" title="全屏显示">
              <i class="layui-icon layui-icon-screen-full"></i>全屏
            </button>
          </div>
        </div>

        <!-- 右侧：窗口控制按钮 -->
        <div class="window-controls">
          <button class="window-btn" id="btn-minimize" title="最小化">
            <i class="layui-icon layui-icon-subtraction"></i>
          </button>
          <button class="window-btn" id="btn-maximize" title="最大化">
            <i class="layui-icon layui-icon-addition"></i>
          </button>
          <button class="window-btn close" id="btn-close" title="关闭">
            <i class="layui-icon layui-icon-close"></i>
          </button>
        </div>
      </div>

      <!-- 图形显示区域 -->
      <div id="mynetwork"></div>
    </div>
    <div id="customContextMenu" class="custom-context-menu">
      <ul>
        <li onclick="showNodeDetails()">
          <i class="layui-icon">&#xe60a;</i>查看详情
        </li>
        <!--        <li onclick="expandNode()"><i class="layui-icon">&#xe623;</i>展开节点</li>
                <li onclick="collapseNode()"><i class="layui-icon">&#xe625;</i>折叠节点</li>-->
        <li onclick="copyNodeInfo()">
          <i class="layui-icon">&#xe64e;</i>复制信息
        </li>
      </ul>
    </div>

    <div id="copySuccess" class="copy-success">
      <i class="layui-icon">&#xe616;</i> 复制成功！
    </div>

    <script type="text/javascript">
      // 全局变量
      var network = null;
      var selectedNodeId = null;
      var selectedNodeData = null;

      // ===== ① 全局：保存初始坐标 =====
      var initialPositions = {};

      //初始化加载数据
      layui.use(["jquery", "layer", "form"], function () {
        var $ = layui.$;
        var layer = layui.layer;
        var form = layui.form;

        var tipsIndex = null; // 用于存储当前 tips 的索引
        var closeTimer = null; // 用于存储关闭 tips 的定时器

        // 封装一个关闭 tips 的函数
        function closeTips() {
          if (tipsIndex) {
            layer.close(tipsIndex);
            tipsIndex = null;
          }
        }

        // --- 鼠标进入触发元素 ---
        $("#tool-settings-btn").on("mouseenter", function () {
          // 如果鼠标再次进入，清除可能存在的关闭定时器
          if (closeTimer) {
            clearTimeout(closeTimer);
            closeTimer = null;
          }

          // 如果 tips 已经存在，则不再重复创建
          if (tipsIndex) {
            return;
          }

          var dropdownHtml =
            '<div class="layui-form" lay-filter="binaryTreeForm">' +
            '<div class="dropdown-content">' +
            '<input type="checkbox" name="option1" title="标签选项一" lay-skin="primary" checked>' +
            '<input type="checkbox" name="option2" title="标签选项二" lay-skin="primary">' +
            '<input type="checkbox" name="option3" title="标签选项三" lay-skin="primary">' +
            "</div>" +
            "</div>";

          // 创建 tips 并保存索引
          tipsIndex = layer.tips(dropdownHtml, this, {
            tips: [4, "#fff"],
            time: 0, // 必须为0，手动管理关闭
            // tipsMore: true, // 我们不再需要这个，因为我们自己处理 mouseenter/mouseleave
            skin: "layui-layer-molv",
            success: function (tipsLayero, index) {
              // 渲染表单
              form.render("checkbox", "binaryTreeForm");

              // --- 关键：为 tips 层本身也绑定鼠标事件 ---

              // 鼠标进入 tips 层，取消关闭定时器
              tipsLayero.on("mouseenter", function () {
                if (closeTimer) {
                  clearTimeout(closeTimer);
                  closeTimer = null;
                }
              });

              // 鼠标离开 tips 层，启动关闭定时器
              tipsLayero.on("mouseleave", function () {
                closeTimer = setTimeout(function () {
                  closeTips();
                }, 100); // 100毫秒延迟
              });

              // 监听多选框事件
              form.on("checkbox(binaryTreeForm)", function (data) {
                console.log(
                  "多选框状态改变:",
                  data.elem.title,
                  "是否选中:",
                  data.elem.checked
                );
              });
            },
          });
        });

        // --- 鼠标离开触发元素 ---
        $("#tool-settings-btn").on("mouseleave", function () {
          // 启动一个关闭定时器
          closeTimer = setTimeout(function () {
            closeTips();
          }, 100); // 100毫秒延迟
        });

        // 解析URL参数函数
        function getQueryParams() {
          const params = {};
          const queryString = window.location.search.substring(1);
          const pairs = queryString.split("&");

          for (let i = 0; i < pairs.length; i++) {
            const pair = pairs[i].split("=");
            const key = decodeURIComponent(pair[0]);
            const value = decodeURIComponent(pair[1] || "");
            params[key] = value;
          }

          return params;
        }

        const params = getQueryParams();
        // 获取容器
        var container = document.getElementById("mynetwork");
        $.ajax({
          url: commonUrl.baseUrl + "userTransAnalysis/dataAnalysis0822",
          contentType: "application/x-www-form-urlencoded",
          type: "get",
          dataType: "json",
          xhrFields: {
            withCredentials: true,
          },
          headers: {
            Authorization: "Dwqsk " + tokens,
          },
          data: {
            psrId: params.psrId,
          },
          success: function (data) {
            let nodes = [];
            let yijiaruLine = [];
            let dataList = data.dvList;
            let dvList = data.pocessedVLList;
            for (let i = 0; i < dataList.length; i++) {
              let data = dataList[i];
              data.label = data.psrTypeName;
              data.data_id = data.id;

              data.shape = "image";
              let id = data.geoPsrId;

              data.id = id;
              if (data.psrTypeName == undefined || data.psrTypeName == "") {
                if (data.psrType == "3112") {
                  data.psrTypeName = "计量箱";
                }
                if (data.psrType == "3218000") {
                  data.psrTypeName = "低压用户接入点";
                }
              }

              data.image = "./image/AIChat.png";
              if (data.psrTypeName == "计量箱") {
                data.image = "./image/align_top.png";
              }
              if (data.psrTypeName == "墙支架") {
                data.image = "./image/larger.png";
              }
              if (data.psrTypeName == "低压用户接入点") {
                data.image = "./image/smaller.png";
              }
              if (data.psrTypeName == "柱上变压器（公用变）") {
                data.image = "./image/tower_all.png";
              }
              if (data.psrTypeName == "断路器") {
                data.image = "./image/tower_ty.png";
              }

              // 添加标题用于悬停提示
              data.title = `ID: ${data.data_id}
                        类型: ${data.psrTypeName}
                        连接: ${data.connection}
                    `;

              nodes.push(data);
            }

            let edges = [];
            for (let i = 0; i < dvList.length; i++) {
              let dvlist = dvList[i];
              edges.push({
                from: dvlist.startGeoPsrId,
                to: dvlist.endGeoPsrId,
              });

              //     edges.push(data)
            }

            console.log("页面显示图元数量=" + nodes.length);

            var nodes1 = new vis.DataSet(nodes);
            var edges1 = new vis.DataSet(edges);
            // 将数据赋值给vis 数据格式化器
            var data = {
              nodes: nodes1,
              edges: edges1,
            };

            // 配置选项
            var options = {
              layout: {
                hierarchical: {
                  enabled: true,
                  direction: "LR", // 改为横向（Left → Right）
                  sortMethod: "hubsize", // hubsize 一般能减少交叉
                  shakeTowards: "roots", // 尽量往根对齐，减少交错
                  nodeSpacing: 220, // 同层节点间距（可以调大/调小）
                  levelSeparation: 280, // 层与层之间的距离
                },
              },
              nodes: {
                size: 30,
                font: {
                  size: 14,
                  face: "Tahoma",
                },
                borderWidth: 2,
              },
              edges: {
                width: 2,
                color: {
                  color: "#848484",
                  highlight: "#555555",
                },
                smooth: {
                  enabled: false,
                },
                arrows: {
                  to: {
                    enabled: true,
                    scaleFactor: 1,
                  },
                },
              },
              physics: {
                enabled: false,
              },
              interaction: {
                hover: true,
                tooltipDelay: 200,
                navigationButtons: true,
                keyboard: true,
                dragNodes: true,
              },
            };

            // 初始化关系图
            network = new vis.Network(container, data, options);

            // 首帧绘制完成后记录所有节点位置
            network.once("afterDrawing", function () {
              initialPositions = network.getPositions(nodes1.getIds());
              // 初始化工具栏功能
              initToolbarEvents();
            });

            function snapshotPositions() {
              initialPositions = network.getPositions(nodes1.getIds());
            }

            // 初始化下拉菜单
            function initDropdownMenu() {
              var $trigger = $("#dropdown-trigger");
              var $menu = $("#dropdown-menu");
              var isMenuOpen = false;

              // 点击触发器
              $trigger.click(function (e) {
                e.stopPropagation();
                if (isMenuOpen) {
                  closeDropdownMenu();
                } else {
                  openDropdownMenu();
                }
              });

              // 鼠标悬停触发器
              $trigger.mouseenter(function () {
                if (!isMenuOpen) {
                  openDropdownMenu();
                }
              });

              // 鼠标离开下拉区域
              $(".dropdown-label").mouseleave(function () {
                setTimeout(function () {
                  if (!$(".dropdown-label:hover").length) {
                    closeDropdownMenu();
                  }
                }, 100);
              });

              // 阻止菜单内部点击关闭菜单
              $menu.click(function (e) {
                e.stopPropagation();
              });

              // 点击页面其他地方关闭菜单
              $(document).click(function () {
                closeDropdownMenu();
              });

              // 应用设置按钮
              $("#apply-settings").click(function () {
                applyDropdownSettings();
                closeDropdownMenu();
              });

              // 重置设置按钮
              $("#reset-settings").click(function () {
                resetDropdownSettings();
              });

              function openDropdownMenu() {
                $menu.addClass("show");
                $trigger.addClass("active");
                isMenuOpen = true;
                // 重新渲染layui表单
                form.render();
              }

              function closeDropdownMenu() {
                $menu.removeClass("show");
                $trigger.removeClass("active");
                isMenuOpen = false;
              }
            }

            // 应用下拉菜单设置
            function applyDropdownSettings() {
              if (!network) return;

              // 获取显示选项
              var showLabels = $('input[name="showLabels"]').prop("checked");
              var showEdges = $('input[name="showEdges"]').prop("checked");
              var showIcons = $('input[name="showIcons"]').prop("checked");

              // 获取主题选项
              var theme = $('input[name="theme"]:checked').val();

              // 获取节点类型筛选
              var selectedTypes = [];
              $('input[name="nodeType"]:checked').each(function () {
                selectedTypes.push($(this).val());
              });

              // 应用显示设置
              var nodeOptions = {};
              var edgeOptions = {};

              // 节点标签显示/隐藏
              if (showLabels) {
                nodeOptions.font = { size: 14 };
              } else {
                nodeOptions.font = { size: 0 };
              }

              // 连接线显示/隐藏
              if (showEdges) {
                edgeOptions.hidden = false;
              } else {
                edgeOptions.hidden = true;
              }

              // 应用主题
              if (theme === "dark") {
                nodeOptions.color = {
                  background: "#2f2f2f",
                  border: "#555555",
                };
                nodeOptions.font = Object.assign(nodeOptions.font || {}, {
                  color: "#ffffff",
                });
                edgeOptions.color = { color: "#666666" };
                $("#mynetwork").css("background-color", "#1a1a1a");
              } else {
                nodeOptions.color = {
                  background: "#ffffff",
                  border: "#2B7CE9",
                };
                nodeOptions.font = Object.assign(nodeOptions.font || {}, {
                  color: "#343434",
                });
                edgeOptions.color = { color: "#848484" };
                $("#mynetwork").css("background-color", "#ffffff");
              }

              // 应用设置到网络
              network.setOptions({
                nodes: nodeOptions,
                edges: edgeOptions,
              });

              // 节点类型筛选（简化版本）
              filterNodesByType(selectedTypes);

              layer.msg("设置已应用", { icon: 1 });
            }

            // 重置下拉菜单设置
            function resetDropdownSettings() {
              // 重置所有复选框和单选框到默认状态
              $('input[name="showLabels"]').prop("checked", true);
              $('input[name="showEdges"]').prop("checked", true);
              $('input[name="showIcons"]').prop("checked", true);
              $('input[name="nodeType"]').prop("checked", true);
              $('input[name="theme"][value="light"]').prop("checked", true);

              // 重新渲染表单
              form.render();

              layer.msg("设置已重置", { icon: 1 });
            }

            // 按类型筛选节点（简化版本）
            function filterNodesByType(selectedTypes) {
              if (!network) return;

              // 如果选择了"全部类型"，显示所有节点
              if (selectedTypes.includes("all")) {
                // 显示所有节点
                var allNodes = network.body.data.nodes.get();
                allNodes.forEach(function (node) {
                  network.body.data.nodes.update({
                    id: node.id,
                    hidden: false,
                  });
                });
                return;
              }

              // 根据选择的类型显示/隐藏节点
              var allNodes = network.body.data.nodes.get();
              allNodes.forEach(function (node) {
                var nodeType = node.psrTypeName || "";
                var shouldShow = selectedTypes.some(function (type) {
                  return nodeType.includes(type);
                });
                network.body.data.nodes.update({
                  id: node.id,
                  hidden: !shouldShow,
                });
              });
            }

            // 初始化工具栏事件
            function initToolbarEvents() {
              // 初始化下拉菜单
              initDropdownMenu();

              // 适应窗口
              $("#btn-fit").click(function () {
                network.fit({
                  animation: { duration: 500, easingFunction: "easeInOutQuad" },
                });
              });

              // 重置位置
              $("#btn-reset").click(function () {
                // 重置所有节点位置
                if (initialPositions) {
                  network.setPositions(initialPositions);
                }
                network.fit({
                  animation: { duration: 500, easingFunction: "easeInOutQuad" },
                });
              });

              // 布局切换 - 横向
              $("#btn-layout-lr").click(function () {
                if (!$(this).hasClass("active")) {
                  switchLayout("LR");
                  $('.toolbar-btn[id^="btn-layout-"]').removeClass("active");
                  $(this).addClass("active");
                }
              });

              // 布局切换 - 纵向
              $("#btn-layout-tb").click(function () {
                if (!$(this).hasClass("active")) {
                  switchLayout("UD");
                  $('.toolbar-btn[id^="btn-layout-"]').removeClass("active");
                  $(this).addClass("active");
                }
              });

              // 布局切换 - 反向
              $("#btn-layout-rl").click(function () {
                if (!$(this).hasClass("active")) {
                  switchLayout("RL");
                  $('.toolbar-btn[id^="btn-layout-"]').removeClass("active");
                  $(this).addClass("active");
                }
              });

              // 搜索节点
              $("#btn-search").click(function () {
                showSearchDialog();
              });

              // 高亮路径
              $("#btn-highlight").click(function () {
                toggleHighlightMode();
                // 更新按钮状态
                if (highlightMode) {
                  $(this).addClass("active");
                } else {
                  $(this).removeClass("active");
                }
              });

              // 导出图片
              $("#btn-export").click(function () {
                exportNetworkImage();
              });

              // 全屏显示
              $("#btn-fullscreen").click(function () {
                toggleFullscreen();
              });

              // 窗口控制按钮
              $("#btn-minimize").click(function () {
                // 如果在iframe中，通知父页面最小化
                if (window.parent && window.parent !== window) {
                  try {
                    window.parent.postMessage(
                      {
                        type: "BINARY_TREE_WINDOW_CONTROL",
                        action: "minimize",
                      },
                      "*"
                    );
                  } catch (e) {
                    layer.msg("最小化功能需要在弹窗中使用", { icon: 2 });
                  }
                } else {
                  layer.msg("最小化功能需要在弹窗中使用", { icon: 2 });
                }
              });

              $("#btn-maximize").click(function () {
                // 如果在iframe中，通知父页面最大化
                if (window.parent && window.parent !== window) {
                  try {
                    window.parent.postMessage(
                      {
                        type: "BINARY_TREE_WINDOW_CONTROL",
                        action: "maximize",
                      },
                      "*"
                    );
                  } catch (e) {
                    layer.msg("最大化功能需要在弹窗中使用", { icon: 2 });
                  }
                } else {
                  layer.msg("最大化功能需要在弹窗中使用", { icon: 2 });
                }
              });

              $("#btn-close").click(function () {
                // 如果在iframe中，通知父页面关闭
                if (window.parent && window.parent !== window) {
                  try {
                    window.parent.postMessage(
                      {
                        type: "BINARY_TREE_WINDOW_CONTROL",
                        action: "close",
                      },
                      "*"
                    );
                  } catch (e) {
                    layer.msg("关闭功能需要在弹窗中使用", { icon: 2 });
                  }
                } else {
                  // 直接页面访问时关闭窗口
                  if (confirm("确定要关闭页面吗？")) {
                    window.close();
                  }
                }
              });
            }

            //拖动结束时，把被拖动的节点移回原位
            network.on("dragEnd", function (params) {
              if (!params.nodes || params.nodes.length === 0) return;
              params.nodes.forEach(function (id) {
                var pos = initialPositions[id];
                if (pos) network.moveNode(id, pos.x, pos.y);
              });
              network.fit({
                animation: { duration: 300, easingFunction: "easeInOutQuad" },
              });
            });

            // 添加右键菜单事件
            network.on("oncontext", function (params) {
              console.log("--- 鼠标右键事件 (oncontext) 触发 ---");
              console.log("事件参数 (params):", params);
              params.event.preventDefault();

              if (params.nodes && params.nodes.length > 0) {
                selectedNodeId = params.nodes[0];
                selectedNodeData = nodes1.get(selectedNodeId);

                console.log("右键点击了节点 ID:", selectedNodeId);

                var menu = document.getElementById("customContextMenu");
                menu.style.display = "block";
                menu.style.left = params.pointer.DOM.x + "px";
                menu.style.top = params.pointer.DOM.y + "px";
              } else {
                selectedNodeId = null;
                selectedNodeData = null;
                console.log("右键点击了空白区域。");
                document.getElementById("customContextMenu").style.display =
                  "none";
              }
            });

            // 2. 添加单击事件 (click)
            network.on("click", function (params) {
              console.log("--- 鼠标单击事件 (click) 触发 ---");
              console.log("事件参数 (params):", params);
              document.getElementById("customContextMenu").style.display =
                "none";
              if (params.nodes && params.nodes.length > 0) {
                var clickedNodeId = params.nodes[0];
                var clickedNodeData = nodes1.get(clickedNodeId);

                console.log("单击了节点 ID:", clickedNodeId);
                console.log("节点数据:", clickedNodeData);
              } else {
                console.log("单击了空白区域。");
              }
            });

            // 3. 添加双击事件 (doubleClick)
            network.on("doubleClick", function (params) {
              console.log("--- 鼠标双击事件 (doubleClick) 触发 ---");
              console.log("事件参数 (params):", params);

              if (params.nodes && params.nodes.length > 0) {
                selectedNodeId = params.nodes[0];
                selectedNodeData = nodes1.get(selectedNodeId);

                console.log(
                  "双击了节点 ID:",
                  selectedNodeId,
                  "，准备显示详情。"
                );
                showNodeDetails();
              } else {
                console.log("双击了空白区域。");
              }
            });
          },
        });
      });

      // 显示节点详情
      function showNodeDetails() {
        if (!selectedNodeData) return;

        // 隐藏右键菜单
        document.getElementById("customContextMenu").style.display = "none";

        // 使用layui的弹窗
        layer.open({
          type: 1,
          title: "节点详情 - " + selectedNodeData.label,
          area: ["500px", "400px"],
          content: createNodeDetailContent(selectedNodeData),
          btn: ["关闭"],
          yes: function (index, layero) {
            layer.close(index);
          },
        });
      }

      // 创建节点详情内容
      function createNodeDetailContent(nodeData) {
        let content = `
            <div class="node-detail-content">
                <div class="node-detail-item">
                    <span class="node-detail-label">节点ID:</span>
                    <span class="node-detail-value">${nodeData.id || ""}</span>
                </div>
                <div class="node-detail-item">
                    <span class="node-detail-label">数据ID:</span>
                    <span class="node-detail-value">${
                      nodeData.data_id || ""
                    }</span>
                </div>
                <div class="node-detail-item">
                    <span class="node-detail-label">类型名称:</span>
                    <span class="node-detail-value">${
                      nodeData.psrTypeName || ""
                    }</span>
                </div>
                <div class="node-detail-item">
                    <span class="node-detail-label">连接信息:</span>
                    <span class="node-detail-value">${
                      nodeData.connection || ""
                    }</span>
                </div>
        `;

        // 添加其他可能存在的属性
        for (let key in nodeData) {
          if (
            ![
              "id",
              "data_id",
              "psrTypeName",
              "connection",
              "label",
              "title",
            ].includes(key)
          ) {
            content += `
                    <div class="node-detail-item">
                        <span class="node-detail-label" style="word-wrap: break-word">${key}:</span>
                        <span class="node-detail-value">${
                          nodeData[key] || ""
                        }</span>
                    </div>
                `;
          }
        }

        content += `</div>`;
        return content;
      }

      // 复制节点信息
      function copyNodeInfo() {
        if (!selectedNodeData) return;

        // 隐藏右键菜单
        document.getElementById("customContextMenu").style.display = "none";

        // 构建要复制的文本内容
        let textToCopy = `节点详情信息\n`;
        textToCopy += `============\n`;
        textToCopy += `节点ID: ${selectedNodeData.id || "无"}\n`;
        textToCopy += `数据ID: ${selectedNodeData.data_id || "无"}\n`;
        textToCopy += `类型名称: ${selectedNodeData.psrTypeName || "无"}\n`;
        textToCopy += `连接信息: ${selectedNodeData.connection || "无"}\n`;

        // 添加其他属性
        for (let key in selectedNodeData) {
          if (
            ![
              "id",
              "data_id",
              "psrTypeName",
              "connection",
              "label",
              "title",
            ].includes(key)
          ) {
            textToCopy += `${key}: ${selectedNodeData[key] || "无"}\n`;
          }
        }

        // 使用现代 Clipboard API 复制文本
        navigator.clipboard
          .writeText(textToCopy)
          .then(function () {
            // 显示复制成功提示
            showCopySuccess();
          })
          .catch(function (err) {
            // 如果 Clipboard API 不可用，使用备用方法
            copyTextFallback(textToCopy);
          });
      }

      // 显示复制成功提示
      function showCopySuccess() {
        var successMsg = document.getElementById("copySuccess");
        successMsg.style.display = "block";

        // 2秒后自动隐藏
        setTimeout(function () {
          successMsg.style.display = "none";
        }, 2000);
      }

      // 复制文本的备用方法
      function copyTextFallback(text) {
        // 创建临时textarea元素
        var textarea = document.createElement("textarea");
        textarea.value = text;
        textarea.style.position = "fixed";
        textarea.style.opacity = "0";

        document.body.appendChild(textarea);
        textarea.select();

        try {
          var successful = document.execCommand("copy");
          if (successful) {
            showCopySuccess();
          } else {
            layer.msg("复制失败，请手动复制", { icon: 2 });
          }
        } catch (err) {
          layer.msg("复制失败: " + err, { icon: 2 });
        }

        document.body.removeChild(textarea);
      }

      // 点击页面其他地方隐藏菜单
      document.addEventListener("click", function (e) {
        var menu = document.getElementById("customContextMenu");
        if (menu.style.display === "block" && !menu.contains(e.target)) {
          menu.style.display = "none";
        }
      });

      // ========== 工具栏功能函数 ==========

      // 切换布局
      function switchLayout(direction) {
        if (!network) return;

        var currentOptions = network.getOptionsFromConfigurator();
        var newOptions = {
          layout: {
            hierarchical: {
              enabled: true,
              direction: direction, // 'LR', 'RL', 'UD', 'DU'
              sortMethod: "hubsize",
              shakeTowards: "roots",
              nodeSpacing: 220,
              levelSeparation: 280,
            },
          },
        };

        network.setOptions(newOptions);

        // 重新布局后适应窗口
        setTimeout(function () {
          network.fit({
            animation: { duration: 500, easingFunction: "easeInOutQuad" },
          });
        }, 100);
      }

      // 显示搜索对话框
      function showSearchDialog() {
        layer.prompt(
          {
            title: "搜索节点",
            formType: 0, // 输入框类型
            value: "", // 默认值
            area: ["300px", "150px"],
          },
          function (value, index, elem) {
            if (value && value.trim()) {
              searchNode(value.trim());
            }
            layer.close(index);
          }
        );
      }

      // 搜索节点功能
      function searchNode(searchText) {
        if (!network) return;

        var nodes = network.body.data.nodes;
        var foundNodes = [];

        nodes.forEach(function (node) {
          if (
            node.label &&
            node.label.toLowerCase().includes(searchText.toLowerCase())
          ) {
            foundNodes.push(node.id);
          }
        });

        if (foundNodes.length > 0) {
          // 高亮找到的节点
          network.selectNodes(foundNodes);
          network.focus(foundNodes[0], {
            scale: 1.5,
            animation: { duration: 500, easingFunction: "easeInOutQuad" },
          });

          layer.msg(`找到 ${foundNodes.length} 个匹配的节点`, { icon: 1 });
        } else {
          layer.msg("未找到匹配的节点", { icon: 2 });
        }
      }

      // 导出网络图片
      function exportNetworkImage() {
        if (!network) return;

        try {
          var canvas = network.canvas.frame.canvas;
          var dataURL = canvas.toDataURL("image/png");

          // 创建下载链接
          var link = document.createElement("a");
          link.download = "二叉树汉字图_" + new Date().getTime() + ".png";
          link.href = dataURL;

          // 触发下载
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);

          layer.msg("图片导出成功", { icon: 1 });
        } catch (error) {
          console.error("导出失败:", error);
          layer.msg("导出失败，请重试", { icon: 2 });
        }
      }

      // 高亮模式状态
      var highlightMode = false;
      var highlightedNodes = [];
      var highlightedEdges = [];

      // 切换高亮模式
      function toggleHighlightMode() {
        highlightMode = !highlightMode;
        var btn = $("#btn-highlight");

        if (highlightMode) {
          btn.addClass("active");
          layer.msg("点击节点查看路径高亮", { icon: 1, time: 2000 });
          // 绑定节点点击事件用于高亮
          network.on("click", highlightNodePath);
        } else {
          btn.removeClass("active");
          // 清除高亮
          clearHighlight();
          // 移除高亮点击事件
          network.off("click", highlightNodePath);
          layer.msg("高亮模式已关闭", { icon: 1, time: 1000 });
        }
      }

      // 高亮节点路径
      function highlightNodePath(params) {
        if (!highlightMode || !params.nodes || params.nodes.length === 0)
          return;

        var nodeId = params.nodes[0];
        var connectedNodes = network.getConnectedNodes(nodeId);
        var connectedEdges = network.getConnectedEdges(nodeId);

        // 清除之前的高亮
        clearHighlight();

        // 设置新的高亮
        highlightedNodes = [nodeId].concat(connectedNodes);
        highlightedEdges = connectedEdges;

        // 更新节点和边的样式
        var updateNodes = highlightedNodes.map(function (id) {
          return {
            id: id,
            color: { background: "#ff6b6b", border: "#ff4757" },
          };
        });

        var updateEdges = highlightedEdges.map(function (id) {
          return { id: id, color: { color: "#ff6b6b" }, width: 4 };
        });

        network.body.data.nodes.update(updateNodes);
        network.body.data.edges.update(updateEdges);

        layer.msg(
          `高亮了 ${highlightedNodes.length} 个节点和 ${highlightedEdges.length} 条连线`,
          { icon: 1, time: 1500 }
        );
      }

      // 清除高亮
      function clearHighlight() {
        if (highlightedNodes.length > 0 || highlightedEdges.length > 0) {
          // 恢复节点样式
          var resetNodes = highlightedNodes.map(function (id) {
            return { id: id, color: null };
          });

          // 恢复边样式
          var resetEdges = highlightedEdges.map(function (id) {
            return { id: id, color: { color: "#848484" }, width: 2 };
          });

          network.body.data.nodes.update(resetNodes);
          network.body.data.edges.update(resetEdges);

          highlightedNodes = [];
          highlightedEdges = [];
        }
      }

      // 全屏功能
      function toggleFullscreen() {
        var elem = document.documentElement;

        if (!document.fullscreenElement) {
          // 进入全屏
          if (elem.requestFullscreen) {
            elem.requestFullscreen();
          } else if (elem.webkitRequestFullscreen) {
            elem.webkitRequestFullscreen();
          } else if (elem.msRequestFullscreen) {
            elem.msRequestFullscreen();
          }
          $("#btn-fullscreen").addClass("active");
          layer.msg("已进入全屏模式", { icon: 1, time: 1000 });
        } else {
          // 退出全屏
          if (document.exitFullscreen) {
            document.exitFullscreen();
          } else if (document.webkitExitFullscreen) {
            document.webkitExitFullscreen();
          } else if (document.msExitFullscreen) {
            document.msExitFullscreen();
          }
          $("#btn-fullscreen").removeClass("active");
          layer.msg("已退出全屏模式", { icon: 1, time: 1000 });
        }
      }

      // 监听全屏状态变化
      document.addEventListener("fullscreenchange", function () {
        if (!document.fullscreenElement) {
          $("#btn-fullscreen").removeClass("active");
        }
      });

      // ========== 下拉菜单功能函数 ==========

      // 主题切换
      var currentTheme = "light";
      function toggleTheme() {
        if (currentTheme === "light") {
          // 切换到深色主题
          applyDarkTheme();
          currentTheme = "dark";
          layer.msg("已切换到深色主题", { icon: 1 });
        } else {
          // 切换到浅色主题
          applyLightTheme();
          currentTheme = "light";
          layer.msg("已切换到浅色主题", { icon: 1 });
        }
      }

      function applyDarkTheme() {
        if (network) {
          network.setOptions({
            nodes: {
              color: {
                background: "#2f2f2f",
                border: "#555555",
              },
              font: {
                color: "#ffffff",
              },
            },
            edges: {
              color: {
                color: "#666666",
              },
            },
          });
          $("#mynetwork").css("background-color", "#1a1a1a");
        }
      }

      function applyLightTheme() {
        if (network) {
          network.setOptions({
            nodes: {
              color: {
                background: "#ffffff",
                border: "#2B7CE9",
              },
              font: {
                color: "#343434",
              },
            },
            edges: {
              color: {
                color: "#848484",
              },
            },
          });
          $("#mynetwork").css("background-color", "#ffffff");
        }
      }

      // 节点标签显示/隐藏
      var labelsVisible = true;
      function toggleNodeLabels() {
        if (!network) return;

        labelsVisible = !labelsVisible;

        network.setOptions({
          nodes: {
            font: {
              size: labelsVisible ? 14 : 0,
            },
          },
        });

        layer.msg(labelsVisible ? "节点标签已显示" : "节点标签已隐藏", {
          icon: 1,
        });
      }

      // 连线样式对话框
      function showEdgeStyleDialog() {
        var edgeStyleHtml = `
          <div style="padding: 20px;">
            <div class="layui-form">
              <div class="layui-form-item">
                <label class="layui-form-label">连线类型</label>
                <div class="layui-input-block">
                  <input type="radio" name="edgeType" value="straight" title="直线" checked>
                  <input type="radio" name="edgeType" value="smooth" title="平滑">
                  <input type="radio" name="edgeType" value="curved" title="弯曲">
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">连线宽度</label>
                <div class="layui-input-block">
                  <input type="range" id="edgeWidthSlider" min="1" max="8" value="2" style="width: 200px;">
                  <span id="edgeWidthValue">2</span>px
                </div>
              </div>
            </div>
          </div>
        `;

        layer.open({
          type: 1,
          title: "连线样式设置",
          area: ["400px", "250px"],
          content: edgeStyleHtml,
          btn: ["应用", "取消"],
          yes: function (index) {
            applyEdgeStyle();
            layer.close(index);
          },
        });

        // 绑定滑块事件
        $("#edgeWidthSlider").on("input", function () {
          $("#edgeWidthValue").text($(this).val());
        });
      }

      function applyEdgeStyle() {
        if (!network) return;

        var edgeType = $('input[name="edgeType"]:checked').val();
        var edgeWidth = parseInt($("#edgeWidthSlider").val());

        var smoothOptions = {
          straight: { enabled: false },
          smooth: { enabled: true, type: "continuous" },
          curved: { enabled: true, type: "curvedCW", roundness: 0.2 },
        };

        network.setOptions({
          edges: {
            width: edgeWidth,
            smooth: smoothOptions[edgeType],
          },
        });

        layer.msg("连线样式已应用", { icon: 1 });
      }

      // 节点筛选对话框
      function showNodeFilterDialog() {
        var filterHtml = `
          <div style="padding: 20px;">
            <div class="layui-form">
              <div class="layui-form-item">
                <label class="layui-form-label">按类型筛选</label>
                <div class="layui-input-block">
                  <input type="checkbox" name="nodeType" value="all" title="全部" checked>
                  <input type="checkbox" name="nodeType" value="计量箱" title="计量箱">
                  <input type="checkbox" name="nodeType" value="墙支架" title="墙支架">
                  <input type="checkbox" name="nodeType" value="断路器" title="断路器">
                </div>
              </div>
            </div>
          </div>
        `;

        layer.open({
          type: 1,
          title: "节点筛选",
          area: ["350px", "200px"],
          content: filterHtml,
          btn: ["应用筛选", "重置", "取消"],
          btn1: function (index) {
            applyNodeFilter();
            layer.close(index);
          },
          btn2: function () {
            resetNodeFilter();
          },
        });
      }

      function applyNodeFilter() {
        // 这里可以根据选择的类型筛选节点
        layer.msg("节点筛选功能开发中...", { icon: 2 });
      }

      function resetNodeFilter() {
        // 重置筛选，显示所有节点
        layer.msg("筛选已重置", { icon: 1 });
      }

      // 高级设置对话框
      function showAdvancedSettings() {
        var settingsHtml = `
          <div style="padding: 20px;">
            <div class="layui-form">
              <div class="layui-form-item">
                <label class="layui-form-label">动画效果</label>
                <div class="layui-input-block">
                  <input type="checkbox" name="animation" title="启用动画" checked>
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">物理引擎</label>
                <div class="layui-input-block">
                  <input type="checkbox" name="physics" title="启用物理引擎">
                </div>
              </div>
              <div class="layui-form-item">
                <label class="layui-form-label">导航按钮</label>
                <div class="layui-input-block">
                  <input type="checkbox" name="navigation" title="显示导航按钮" checked>
                </div>
              </div>
            </div>
          </div>
        `;

        layer.open({
          type: 1,
          title: "高级设置",
          area: ["350px", "250px"],
          content: settingsHtml,
          btn: ["保存", "取消"],
          yes: function (index) {
            applyAdvancedSettings();
            layer.close(index);
          },
        });
      }

      function applyAdvancedSettings() {
        // 这里可以应用高级设置
        layer.msg("高级设置功能开发中...", { icon: 2 });
      }
    </script>
  </body>
</html>
